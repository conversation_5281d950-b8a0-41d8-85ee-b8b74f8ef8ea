package com.centerm.cpay.tms.controller;

import com.centerm.cpay.common.domain.task.AdvertJobInOutput;
import com.centerm.cpay.common.dto.app.AdvertListInput;
import com.centerm.cpay.common.dto.app.AdvertOutput;
import com.centerm.cpay.task.service.IAdvertJobService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.miser.common.core.domain.ListOutput;
import org.miser.common.service.IGeneratorService;
import org.miser.core.app.base.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 广告信息查询
 *
 * <AUTHOR> @date 2020/04/09 14:30
 **/
@Api(tags = "【运维平台】广告模块")
@RequestMapping("/advert")
@RestController
public class AdvertController extends BaseController {

    @Autowired
    private IAdvertJobService advertJobService;

    @Autowired
    private IGeneratorService generatorService;


    @ApiOperation(value = "查询广告信息列表")
    @PostMapping("/list")
    public ListOutput<AdvertOutput> list(@RequestBody @Validated AdvertListInput advertListInput) {
        List<AdvertJobInOutput>  advertInfoList = advertJobService.selectTmsAdvertJobListByTusn(advertListInput.getTusn());
        List<AdvertOutput> advertOutputList = generatorService.convert(advertInfoList, AdvertOutput.class);
        for (AdvertOutput advertOutput : advertOutputList){
            advertJobService.updateAdvertTaskByJobId(advertOutput.getId(), 1, advertListInput.getTusn());
        }
        return new ListOutput<>(advertOutputList);
    }

}