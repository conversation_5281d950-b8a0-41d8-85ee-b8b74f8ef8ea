package com.centerm.cpay.tms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.centerm.cpay.common.domain.task.DesktopTask;
import com.centerm.cpay.common.domain.task.DownloadJobApp;
import com.centerm.cpay.common.domain.task.DownloadTask;
import com.centerm.cpay.common.domain.terminal.TerminalApp;
import com.centerm.cpay.common.dto.task.DownloadResultInput;
import com.centerm.cpay.task.service.IDesktopTaskService;
import com.centerm.cpay.task.service.IDownloadJobAppService;
import com.centerm.cpay.task.service.IDownloadTaskService;
import com.centerm.cpay.terminal.service.ITerminalAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.miser.common.core.controller.BaseController;
import org.miser.common.service.IGeneratorService;
import org.miser.common.utils.data.CommonUtils;
import org.miser.common.utils.data.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 下载结果上送
 *
 * <AUTHOR>
 * @date 2019/11/11 19:44
 **/
@Slf4j
@Api(tags = "下载结果上送上送")
@RestController
@RequestMapping("download")
public class DownloadResultController extends BaseController {

    @Autowired
    private IGeneratorService generatorService;

    @Autowired
    private IDownloadTaskService downloadTaskService;

    @Autowired
    private IDesktopTaskService desktopTaskService;
    @Autowired
    private ITerminalAppService terminalAppService;
    @Autowired
    private IDownloadJobAppService downloadJobAppService;

    @ApiOperation(value = "下载结果上送")
    @PostMapping("/result")
    @Transactional(rollbackFor = Exception.class)
    public void downloadUpload(@RequestBody @Validated DownloadResultInput downloadResultInput) {
        Boolean isSucces = false;
        @NotEmpty List<DownloadResultInput.TaskInfo> taskInfoList = downloadResultInput.getTaskInfoList().stream().map(
                taskInfo -> {
                    if (taskInfo.getStartTime() != null) {
                        taskInfo.setStartTime(String.valueOf(DateUtils.dateTime(DateUtils.YYYYMMDDHHMMSS, taskInfo.getStartTime()).getTime()));
                    }
                    if (taskInfo.getEndTime() != null) {
                        taskInfo.setEndTime(String.valueOf(DateUtils.dateTime(DateUtils.YYYYMMDDHHMMSS, taskInfo.getEndTime()).getTime()));
                    }
                    return taskInfo;
                }).collect(Collectors.toList());
        if ("1".equals(downloadResultInput.getTaskType())) {
            List<DownloadTask> downloadTaskList = generatorService.convert(taskInfoList, DownloadTask.class);
            //修改下载任务状态
            isSucces = downloadTaskService.updateBatchById(downloadTaskList);
            try {
                for (DownloadTask d : downloadTaskList) {
                    d = downloadTaskService.getById(d.getId());
                    if (!CommonUtils.isEmpty(d)) {
                        if ("5".equals(d.getStatus().toString())) {
                            DownloadJobApp app = downloadJobAppService.getById(d.getJobAppId());
                            if (!CommonUtils.isEmpty(app)) {
                                terminalAppService.remove(new QueryWrapper<TerminalApp>().lambda()
                                        .eq(TerminalApp::getTusn, d.getTusn()).eq(TerminalApp::getAppCode, app.getAppCode()));
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        } else if ("2".equals(downloadResultInput.getTaskType())) {
            List<DesktopTask> desktopTaskList = generatorService.convert(taskInfoList, DesktopTask.class);
            //修改下载任务状态
            isSucces = desktopTaskService.updateBatchById(desktopTaskList);
        } else {
            log.error("任务结果类型不正确");
        }
        log.info("\n下载结果上送:[{}]", isSucces);

    }
}
