package com.centerm.cpay.ams;

import com.centerm.cpay.common.dto.terminal.ActivitionInput;
import org.junit.Test;

/**
 * TmsControllerTest
 *
 * <AUTHOR>
 * @date 2019/12/31 16:39
 **/

public class TmsActivitionControllerTest extends TmsRequestGroupTest {


    @Test
    public void testActive() throws Exception {
        sendRequest("/activation/auto");
    }


    @Override
    public ActivitionInput getRequestBody() {
        ActivitionInput activation = new ActivitionInput();
        activation.setDeptCode("01315999999");
        activation.setFirmName("Centerm");
        activation.setTerminalType("K9");
        activation.setLongitude("119.33022111");
        activation.setLatitude("26.0471255");
        return activation;
    }

}