<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cpay-gateway</artifactId>
        <groupId>com.centerm</groupId>
        <version>4.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cpay-tms</artifactId>
    <packaging>war</packaging>
    <description>app接口</description>
    <dependencies>

        <!-- 核心模块-->
        <dependency>
            <groupId>org.miser</groupId>
            <artifactId>jmiser-app</artifactId>
        </dependency>

        <!--应用服务服务-->
        <dependency>
            <groupId>com.centerm</groupId>
            <artifactId>cpay-app</artifactId>
            <version>4.0.0</version>
        </dependency>

        <!--任务服务-->
        <dependency>
            <groupId>com.centerm</groupId>
            <artifactId>cpay-task</artifactId>
            <version>4.0.0</version>
        </dependency>
        <!--配置文件-->
        <dependency>
            <groupId>com.centerm</groupId>
            <artifactId>cpay-config</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.centerm</groupId>
            <artifactId>cpay-emq-service</artifactId>
            <version>4.0.0</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-war-plugin</artifactId><version>${maven-war-plugin.version}</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <outputFileNameMapping>@{artifactId}@-@{baseVersion}@.@{extension}@</outputFileNameMapping>
                </configuration>
            </plugin>
        </plugins>
        <finalName>cpay-tms</finalName>
    </build>

</project>