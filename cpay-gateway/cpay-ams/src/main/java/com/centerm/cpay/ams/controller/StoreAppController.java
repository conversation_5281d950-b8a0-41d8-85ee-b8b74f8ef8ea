package com.centerm.cpay.ams.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.centerm.cpay.app.constant.FileUploadStatusEnum;
import com.centerm.cpay.common.domain.app.AppInfo;
import com.centerm.cpay.common.domain.terminal.TerminalGroupSet;
import com.centerm.cpay.common.domain.terminal.TerminalInfo;
import com.centerm.cpay.common.dto.app.*;
import com.centerm.cpay.common.dto.terminal.AppListInput;
import com.centerm.cpay.common.enums.app.AppStatusEnum;
import com.centerm.cpay.common.utils.CommonUtils;
import com.centerm.cpay.common.dto.app.UserAndCloneAppInOutput;
import com.centerm.cpay.app.service.IAppInfoService;
import com.centerm.cpay.app.service.ICopsStoreAppService;
import com.centerm.cpay.terminal.service.ITerminalGroupSetService;
import com.centerm.cpay.terminal.service.ITerminalInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.miser.common.core.domain.ListOutput;
import org.miser.common.core.page.TableDataInfo;
import org.miser.common.core.support.Convert;
import org.miser.common.enums.PlatformType;
import org.miser.common.service.IGeneratorService;
import org.miser.common.utils.data.ApiAssert;
import org.miser.common.utils.data.CtUtils;
import org.miser.core.app.base.BaseController;
import org.miser.file.service.IFileService;
import org.miser.system.domain.SysDept;
import org.miser.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 应用信息Controller
 *
 * <AUTHOR>
 * @date 2019-08-26
 */
@Api(tags = "【运维平台】应用信息模块")
@RequestMapping("/app/info")
@RestController
public class StoreAppController extends BaseController {

    @Autowired
    private IAppInfoService appInfoService;

    @Autowired
    private IGeneratorService generatorService;

    @Autowired
    private ITerminalInfoService terminalInfoService;

    @Autowired
    private ITerminalGroupSetService terminalGroupSetService;

    @Autowired
    private ICopsStoreAppService copsStoreAppService;

    @Autowired
    private IFileService fileService;
    @Autowired
    private ISysDeptService deptService;
    public static final String DEPT_SPLIT = ",";

    @ApiOperation(value = "查询应用列表")
    @PostMapping("/list")
    public TableDataInfo<AppQueryOutput> getApkList(@RequestBody AppListInput appListInput) {
        AppQueryInput appQueryInput = generatorService.convert(appListInput, AppQueryInput.class);
        TerminalInfo terminalInfo = terminalInfoService.selectTerminalInfoByTusn(appListInput.getTusn());


        //目前按照机构分组查询（运维平台应用）
        //有组别，查询（本级+上级）该组别应用+（本级+上级）无组别应用
        //无组别，查询（本级+上级）无组别应用
        if (CtUtils.isNotEmpty(terminalInfo.getDeptId())) {
        /*机构下标签关联应用
        appQueryInput.setDeptId(terminalInfo.getDeptId());
        startPage(appQueryInput);
        //根据机构获取机构关联标签，查询相应应用数据
        List<AppQueryOutput> list = appInfoService.queryAppListByDeptLabel(appQueryInput, terminalInfo.getDeptId());
        if (CtUtils.isNotEmpty(list)) {
            return getDataTable(list);
        }*/
            appQueryInput.setStatus(AppStatusEnum.ON_SHELVES.ordinal());//设置上架状态的应用
            appQueryInput.setUploadStatus(FileUploadStatusEnum.SUCCESS.getCode());//设置应用上传状态
            appQueryInput.setTermType(terminalInfo.getTerminalTypeName());//设置终端型号
            //获取本级及所有上级机构
            SysDept sysDept = deptService.selectDeptById(terminalInfo.getDeptId());
            if (CtUtils.isEmpty(sysDept)) {
                ApiAssert.failure("institution.not.exist", "deptId");
            }
            String[] parentsDeptIds = (terminalInfo.getDeptId() + DEPT_SPLIT + sysDept.getAncestors()).split(DEPT_SPLIT);
            appQueryInput.setParentsDeptIds(parentsDeptIds);

            appQueryInput.setParentsDeptExcludeSelfIds(sysDept.getAncestors().split(DEPT_SPLIT));
        }
        //查询终端组别 目前一个终端一个组别
        TerminalGroupSet terminalGroupSet = terminalGroupSetService.getOne(
                new QueryWrapper<TerminalGroupSet>()
                        .lambda().eq(TerminalGroupSet::getTerminalId, terminalInfo.getId())
        );
        appQueryInput.setDeptId(terminalInfo.getDeptId());
        //设置组别信息
        if (CtUtils.isNotEmpty(terminalGroupSet)) {
            appQueryInput.setGroupId(terminalGroupSet.getGroupId());
        } else {//默认应用组别
            appQueryInput.setGroupId(0l);
        }
        startPage(appQueryInput);
        List<AppQueryOutput> list = appInfoService.queryAppList(appQueryInput);
        return getDataTable(list);


    }

    @ApiOperation(value = "查询应用详情")
    @PostMapping("/detail")
    public AppInfoOutput getDetail(@RequestBody @Validated AppDetailInput appDetailInput) {
        //查询运维平台应用
        AppInfo appInfo = new AppInfo();
        appInfo.setStatus(AppStatusEnum.ON_SHELVES.ordinal());
        appInfo.setId(appDetailInput.getAppId());
        appInfo = appInfoService.getOne(new QueryWrapper<AppInfo>(appInfo));

        appInfo.setApkPath(fileService.getFileFullPath(appInfo.getApkPath()));
        appInfo.setIconPath(fileService.getFileFullPath(appInfo.getIconPath()));
        appInfo.setPicPaths(CommonUtils.appendSaveUrl(appInfo.getPicPaths()));

        AppInfoOutput appInfoOutput = generatorService.convert(appInfo, AppInfoOutput.class);
        return appInfoOutput;
    }

    @ApiOperation(value = "查询应用版本")
    @PostMapping("/version")
    public ListOutput<AppVersionOutput> getAppVersion(@RequestBody @Validated AppCodeListInput appVersionInput) {
        appVersionInput.setStatus(AppStatusEnum.ON_SHELVES.ordinal());
        List<AppVersionOutput> appVersionOutputList = appInfoService.queryAppVersion(appVersionInput);
        return new ListOutput<AppVersionOutput>(appVersionOutputList);
    }

    @ApiOperation(value = "查询应用是否可卸载")
    @PostMapping("/uninstallCheck")
    public ListOutput<AppUninstallOutput> uninstallCheck(@RequestBody @Validated AppCodeListInput appCodeListInput) {
        List<String> appCodeList = Convert.toStrList(appCodeListInput.getAppCodes());
        List<AppInfo> appInfoList = appInfoService.list(new QueryWrapper<AppInfo>().lambda().in(AppInfo::getApkCode, appCodeList));
        List<AppUninstallOutput> appUninstallOutputs = generatorService.convert(appInfoList, AppUninstallOutput.class);
        return new ListOutput<AppUninstallOutput>(appUninstallOutputs);
    }

    @ApiOperation(value = "添加应用下载量")
    @PostMapping("/addDownloadNum")
    public boolean countDownload(@RequestBody @Validated AppDetailInput appDetailInput) {
        AppInfo appInfo = appInfoService.getById(appDetailInput.getAppId());
        if (appInfo.getDownloadNum() != null) {
            appInfo.setDownloadNum(appInfo.getDownloadNum() + 1);
        } else {
            appInfo.setDownloadNum(1);
        }
        return appInfoService.updateById(appInfo);
    }
}