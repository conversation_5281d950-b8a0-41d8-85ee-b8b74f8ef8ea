package com.centerm.cpay.ams.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.centerm.cpay.common.domain.app.AppType;
import com.centerm.cpay.common.domain.terminal.TerminalGroupSet;
import com.centerm.cpay.common.domain.terminal.TerminalInfo;
import com.centerm.cpay.common.dto.app.AppQueryInput;
import com.centerm.cpay.common.dto.app.AppQueryOutput;
import com.centerm.cpay.common.dto.app.AppTypOutput;
import com.centerm.cpay.common.dto.terminal.AppListInput;
import com.centerm.cpay.common.enums.app.AppStatusEnum;
import com.centerm.cpay.common.dto.app.UserAndCloneAppInOutput;
import com.centerm.cpay.app.service.IAppInfoService;
import com.centerm.cpay.app.service.IAppTypeService;
import com.centerm.cpay.app.service.ICopsStoreAppService;
import com.centerm.cpay.terminal.service.ITerminalGroupSetService;
import com.centerm.cpay.terminal.service.ITerminalInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.miser.common.core.domain.ListOutput;
import org.miser.common.core.domain.PageInput;
import org.miser.common.enums.PlatformType;
import org.miser.common.service.IGeneratorService;
import org.miser.common.utils.data.ApiAssert;
import org.miser.common.utils.data.CtUtils;
import org.miser.common.utils.data.StringUtils;
import org.miser.core.app.base.BaseController;
import org.miser.file.service.IFileService;
import org.miser.system.domain.SysDept;
import org.miser.system.service.ISysConfigService;
import org.miser.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 应用类型Controller
 *
 * <AUTHOR>
 * @date 2019-08-26
 */
@Api(tags = "【运维平台】应用信息模块")
@RequestMapping("/app/type")
@RestController
public class StoreAppTypeController extends BaseController {

    @Autowired
    private IAppTypeService appTypeService;

    @Autowired
    private IGeneratorService generatorService;

    @Autowired
    private IAppInfoService appInfoService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private IFileService fileService;

    @Autowired
    private ITerminalInfoService terminalInfoService;

    @Autowired
    private ITerminalGroupSetService terminalGroupSetService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ICopsStoreAppService copsStoreAppService;
    public static final String DEPT_SPLIT = ",";


    @ApiOperation(value = "查询应用类型列表应用")
    @PostMapping("/list")
    public ListOutput<AppTypOutput> list(@RequestBody AppListInput appListInput) {
        List<AppType> appTypeList = appTypeService.list();
        if (StringUtils.isNotEmpty(appTypeList)) {
            List<AppTypOutput> appTypOutputList = generatorService.convert(appTypeList, AppTypOutput.class);
            appTypOutputList.forEach(item -> item.setIconPath(fileService.getFileFullPath(item.getIconPath())));
            //查询列表对应应用信息
            PageInput pageInput = new PageInput();
            pageInput.setPageNum(CtUtils.isEmpty(appListInput.getPageNum()) ? 1 : appListInput.getPageNum());
            pageInput.setPageSize(CtUtils.isEmpty(appListInput.getPageSize()) ? 5 : appListInput.getPageSize());

            AppQueryInput appQueryInput = new AppQueryInput();
            appQueryInput.setPageNum(CtUtils.isEmpty(appListInput.getPageNum()) ? 1 : appListInput.getPageNum());
            appQueryInput.setPageSize(CtUtils.isEmpty(appListInput.getPageSize()) ? 5 : appListInput.getPageSize());
            //设置终端信息
            TerminalInfo terminalInfo = terminalInfoService.selectTerminalInfoByTusn(appListInput.getTusn());
            appQueryInput.setDeptId(terminalInfo.getDeptId());
            appQueryInput.setStatus(AppStatusEnum.ON_SHELVES.ordinal());


            //查询终端组别
            List<TerminalGroupSet> terminalGroupList = terminalGroupSetService.list(
                    new QueryWrapper<TerminalGroupSet>()
                            .lambda().eq(TerminalGroupSet::getTerminalId, terminalInfo.getId())
            );
            if (CtUtils.isNotEmpty(terminalGroupList)) {
                //目前一个终端一个组别
                TerminalGroupSet terminalGroupSet = terminalGroupList.get(0);
                appQueryInput.setGroupId(terminalGroupSet.getGroupId());
            } else {//默认应用组别
                appQueryInput.setGroupId(0L);
            }
            //获取本级及所有上级机构
            SysDept sysDept = deptService.selectDeptById(appQueryInput.getDeptId());
            if (CtUtils.isEmpty(sysDept)) {
                ApiAssert.failure("institution.not.exist", "deptId");
            }
            String[] parentsDeptIds = (appQueryInput.getDeptId() + DEPT_SPLIT + sysDept.getAncestors()).split(DEPT_SPLIT);
            appQueryInput.setParentsDeptIds(parentsDeptIds);
            appQueryInput.setParentsDeptExcludeSelfIds(sysDept.getAncestors().split(DEPT_SPLIT));
            appQueryInput.setTermType(terminalInfo.getTerminalTypeName());//设置终端型号

            for (AppTypOutput type : appTypOutputList) {
                appQueryInput.setTypeId(type.getId());
                //设置组别信息
                startPage(pageInput);

                List<AppQueryOutput> list = appInfoService.queryAppList(appQueryInput);
                type.setAppQueryOutputList(list);

            }
            return new ListOutput<>(appTypOutputList);
        }
        return new ListOutput<>(null);
    }

}