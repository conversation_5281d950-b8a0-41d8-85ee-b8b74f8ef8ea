package com.centerm.cpay.ams.controller;

import com.centerm.cpay.common.domain.app.AdvertInfo;
import com.centerm.cpay.common.dto.app.AdvertOutput;
import com.centerm.cpay.app.service.IAdvertInfoService;
import org.miser.core.app.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.miser.common.core.domain.BaseInput;
import org.miser.common.core.domain.ListOutput;
import org.miser.common.service.IGeneratorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 应用商店广告查询
 *
 * <AUTHOR>
 * @date 2019/10/25 11:40
 **/
@Api(tags = "【运维平台】广告模块")
@RequestMapping("/advert")
@RestController
public class StoreAdvertController extends BaseController {

    @Autowired
    private IAdvertInfoService advertInfoService;

    @Autowired
    private IGeneratorService generatorService;

    @ApiOperation(value = "查询广告信息列表")
    @PostMapping("/list")
    public ListOutput<AdvertOutput> list(@RequestBody BaseInput baseInput) {
        List<AdvertInfo> advertInfoList = advertInfoService.list();
        List<AdvertOutput> advertOutputList = generatorService.convert(advertInfoList, AdvertOutput.class);
        return new ListOutput<AdvertOutput>(advertOutputList);
    }

}