package com.centerm.cpay.ams.test;

import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.miser.common.core.domain.BaseInput;
import org.miser.common.core.domain.protocol.RequestHeader;
import org.miser.common.core.domain.protocol.RequestMsg;
import org.miser.common.json.JSONUtils;
import org.miser.common.utils.data.CtUtils;
import org.miser.core.app.service.ISignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

/**
 * TMS分装请求参数
 *
 * <AUTHOR>
 * @date 2020/01/15 10:30
 **/
@Slf4j
public abstract class TmsRequestGroupTest<T extends BaseInput> extends TmsBaseTest {


    @Autowired
    private ISignService signService;

    //终端秘钥
    public static final String privateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALAt0AdlnbBXY04k\n" +
            "+GAKwecG2cn04V3CMO25tLQPHmZCQrlqsIAiES8kFaChrXEuZO+NqByL2SKjBptZ\n" +
            "f2dkP0HUwtrUfAKfZ8xWZNguIBQ3F6BcohaK69E00bKfaAZXGiH81YRnWN5XkL21\n" +
            "y25SxvLL7vb2n86hXzaaRTc7sHvpAgMBAAECgYEApqNudNCkijrd7vgBBbanGQHd\n" +
            "1awNzkTzSqewR+9U7ZwWyJIlR8zxPAmBe2HxT3B2gC8Yp4ePZbeVdDL+0qh7OxKb\n" +
            "fm+ZFgY+zjVHCirwwjHD0hujc720phna//afk1wARB5E5waG9+XSMdijAYHrockT\n" +
            "hOmOU4Rs3gtCpa8VTdECQQDiK4wlo5EHs0+zRa3MWCRmDTIA8EQQxlWHMod3LTvu\n" +
            "LMyKTEGvnaDePjVvxrcrFPtzIPatu03AZHdL7NVFy15fAkEAx2panRsPOQeC2R5G\n" +
            "mvV1mJ5x3M2gc6tS0MlvHGdaA52YMQhnjuMsgVvRSzO0PmvLFrSaSjvaazHx1o+N\n" +
            "paG6twJAZVYFsUecT8rzxutNd+V2SZNX+DuvA6UZ5T2qnKdT1t+TtdN9CuahkXJm\n" +
            "FvL+VvtLvsXiQO1PZQieuxIebOs2GQJBAJuC0Q9bns92kBAJkD4f1YkjZuAua0ZJ\n" +
            "ANhZfhDEeGoBaEI0r626c2ad1+WxeIqFPWeDiB8D9h1KYbJVWL0mFpkCQEbxFXcq\n" +
            "nclEiLNHYARMnSI+vwNFB5A4OxIaApL1UBQ1vtR68F6ULDhXYgBSAVXYzWVfjebR\n" +
            "JXc0OQEbkpSw0Qc=";

    public abstract T getRequestBody();


    public MockMvc mockMvc;
    @Autowired
    private WebApplicationContext webApplicationContext;


    public MvcResult sendRequest(String urlTemplate) throws Exception {
        /*
         * 1、mockMvc.perform执行一个请求。
         * 2、MockMvcRequestBuilders.get("XXX")构造一个请求。
         * 3、ResultActions.param添加请求传值
         * 4、ResultActions.accept(MediaType.TEXT_HTML_VALUE))设置返回类型
         * 5、ResultActions.andExpect添加执行完成后的断言。
         * 6、ResultActions.andDo添加一个结果处理器，表示要对结果做点什么事情
         *   比如此处使用MockMvcResultHandlers.print()输出整个响应结果信息。
         * 7、ResultActions.andReturn表示执行完成后返回相应的结果。
         */
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                .post(urlTemplate)
                // 设置返回值类型为utf-8，否则默认为ISO-8859-1
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON) //执行请求
                .content(getRequestMsg())
        ).andReturn();
        return mvcResult;
    }


    @Before
    public void setup() {
        // 实例化方式一
//        mockMvc = MockMvcBuilders.standaloneSetup(new ActivationController()).build();
        // 实例化方式二
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }


    public String getRequestMsg() {
        RequestMsg<BaseInput> requestMsg = new RequestMsg();
        requestMsg.setHeader(getRequestHeader());
        requestMsg.setBody(getRequestBody());
        requestMsg.setSign(signService.sign(requestMsg, privateKey));
        String requestMsgstr = JSONUtils.toJSONString(requestMsg);
        log.info("\n获取查询请求参数：{}", requestMsgstr);
        return requestMsgstr;
    }


    public RequestHeader getRequestHeader() {
        RequestHeader requestHeader = new RequestHeader();
        requestHeader.setVersion("07");
        requestHeader.setDevMask("I3N1BWcSJQESaRVlBnYDdGUMZBEn");
        requestHeader.setTimestamp(String.valueOf(System.currentTimeMillis()));
        requestHeader.setRandom(CtUtils.getRandom(6));
        requestHeader.setImei("868808035760161");
        requestHeader.setNetMark("48E6C089042F");
        return requestHeader;
    }


}
