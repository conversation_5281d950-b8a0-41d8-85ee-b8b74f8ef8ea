#开发配置
app:
  profile: dev
  ##是否显示demo
  demoEnabled: false
  ###是否签名验证
  sign: false
spring:
  datasource:
    druid:
      # 主库数据源
      master:
#        url: *****************************************************************************************************************************************************************************
#        username: root
#        password: root
        url: ******************************************************************************************************************************************************************************************************
        username: cpay4
        password: Cpay4@2024
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
  redis:
    host: 127.0.0.1
    port: 6379
    password:

    database: 1
  # 远程redis，用于大数据可视化项目的数据维护
  redis2:
    host: 127.0.0.1
    port: 6379
    password:
    database: 0
    timeout: 10000
    pool:
      # 连接池最大连接数（使用负值表示没有限制） 默认 8
      max-active: 2
      # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
      max-wait: -1
      # 连接池中的最大空闲连接 默认 8
      max-idle: 1
      # 连接池中的最小空闲连接 默认 0
      min-idle: 0

emq:
  api:
    url: http://*************:8081/
    username: afb176db30916
    password: MzE4NjA0ODM4NjMwMDYwNjk0NTU2MDQ0MjgwNDgzNjc2MTG
    timeout: 3000
    pool:
      # 最大连接数
      maxTotal: 200
      # 设置每个连接的路由数
      defaultMaxPerRoute: 20
  client:
    url: tcp://*************:1883
    username: java_platform
    password: a290aeda1a
    timeout: 10
    keepAliveInterval: 20
    pool:
      # 最大客户端数
      maxClient: 20
  passwordHash: sha256

r2:
  bucket: test
  url: https://131eb954f37dbeb24f6ad55be71e0af5.r2.cloudflarestorage.com
  download:
    url: https://pub-476da6e80d8b438a88edf7ce60792f49.r2.dev
  access:
    key: 79b173316e5c31ee0b02779514c581d0
  secret:
    key: 9c6144d3c3e83e3ff885a6d15fa7a9daa06a67e52edf1e8dee15844c183a7f5e