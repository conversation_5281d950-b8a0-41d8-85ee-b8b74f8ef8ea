package com.centerm.cpay.common.dto.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 软件信息保存
 *
 * <AUTHOR>
 * @date 2020/2/21
 **/
@Data
public class SoftSaveInputApi implements Serializable {

    @ApiModelProperty(value = "软件编号")
    @NotBlank
    private String code;

    @ApiModelProperty(value = "软件名称")
    @NotBlank
    private String appName;

    @ApiModelProperty(value = "软件类型")
    @NotNull
    private Integer type;

    @ApiModelProperty(value = "内部版本")
    private String versionCode;

    @ApiModelProperty(value = "应用版本")
    private String versionName;

    @ApiModelProperty(value = "软件大小")
    private Integer appSize;

    @ApiModelProperty(value = "软件路径")
    private String appPath;

    @ApiModelProperty(value = "图标文件")
    private String iconPath;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "文件md5")
    private String fileMd5;

    @ApiModelProperty(value = "厂商id")
    private Integer firmId;

    @ApiModelProperty(value = "终端型号")
    private String terminalTypes;

    @ApiModelProperty(value = "机构id")
    private Long deptId;

    @ApiModelProperty(value = "创建时间", hidden = true)
    private Date createTime;

    private String createBy;
    private String updateBy;

    @ApiModelProperty(value = "更新时间", hidden = true)
    private Date updateTime;
}
