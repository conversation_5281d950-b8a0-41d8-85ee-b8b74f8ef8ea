package com.centerm.cpay.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.miser.common.json.JSONUtils;
import org.miser.common.utils.data.CommonUtils;
import org.miser.common.utils.security.base64.Base64Util;

import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.util.*;

/**
 * <AUTHOR>
 * @created 2021/9/15 上午9:07
 */
@Slf4j
public class SignUtil {

    private static final String HMAC_SHA256_ALGORITHM = "HmacSHA256";
    private static final String SIGNATURE = "sign";

    public static boolean verifySign(SortedMap<Object, Object> parameters, String signature, String key) {
        String signData = getSignData(parameters);

        log.debug("signData:" + signData);
        if (CommonUtils.isEmpty(key)) {
            return false;
        }
        String veritySign = encryptSign(signData, key);

        log.info("veritySign:" + veritySign);
        return veritySign.equals(signature);
    }

    public static String encryptSign(String signData, String secret) {
        String sign = "";
        try {
            SecretKeySpec keySpec = new SecretKeySpec(secret.getBytes(), HMAC_SHA256_ALGORITHM);
            Mac mac = Mac.getInstance(HMAC_SHA256_ALGORITHM);
            mac.init(keySpec);
            sign = Base64Util.encode(mac.doFinal(signData.getBytes()));
        }
        catch (Exception e) {
            log.error("Error HmacSHA256 ===========" + e.getMessage());
        }
        return sign;
    }

    /**
     *
     * @param object
     * @param appsecret
     * @return
     */
    public static String generateSignature(Object object, String appsecret)  {

        String request = JSONUtils.toJSONString(object);
        Map requetMap = JSONUtils.jsonToMap(request);

        try {
            String sign = generateSignature(requetMap, appsecret, "HMAC-SHA256");
            return sign;
        }
        catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     *
     * @param data
     * @param appsecret
     * @param signType
     * @return
     * @throws Exception
     */
    public static String generateSignature(final Map<String,Object> data, String appsecret, String signType) throws Exception { // 签名生成方法
        Set keySet = data.keySet();
        String[] keyArray = (String[]) keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        StringBuilder sb = new StringBuilder();
        for (String k : keyArray) {
            if (k.equals("sign")) {
                continue;
            }

            if (data.get(k) == null || "null".equals(data.get(k))) { 				// 参数值为空，则不参与签名
                data.remove(k);
                continue;
            }

            Object value = data.get(k);
            if (value instanceof Integer) {
                value = sb.append(k).append("=").append(value).append("&");
            }
            else {
                if (String.valueOf(value).trim().length() > 0) {
                    sb.append(k).append("=").append(String.valueOf(value).trim()).append("&");
                }
            }
        }

        String sbr = sb.substring(0, sb.length() - 1);
        log.debug("待签名数据:{}" , sbr);
        if ("MD5".equals(signType)) {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] array = md.digest(sbr.getBytes("UTF-8"));
            return Base64.getUrlEncoder().encodeToString(array);					// 16进制base64形式
        }
        else if ("HMAC-SHA256".equals(signType)) {
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(appsecret.getBytes("UTF-8"), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            byte[] array = sha256_HMAC.doFinal(sbr.getBytes("UTF-8"));
            return Base64.getEncoder().encodeToString(array); 					// 16进制base64形式
        }
        return null;
    }

    public static String generateSignatureAES(String data,String appsecret,String signType) throws Exception {
        SecretKeySpec skeySpec = new SecretKeySpec(appsecret.getBytes("UTF-8"), "AES");
        Cipher cipher = Cipher.getInstance(signType); //"算法/模式/补码方式"
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] byteContent = data.getBytes("UTF-8");
        byte[] result = cipher.doFinal(byteContent);
        return Base64.getEncoder().encodeToString(result);
    }

    public static String decrptSignatureAES(String data, String appsecret, String signType) throws Exception {
        SecretKeySpec skeySpec = new SecretKeySpec(appsecret.getBytes("UTF-8"), "AES");
        Cipher cipher = Cipher.getInstance(signType);//"算法/模式/补码方式"
        cipher.init(Cipher.DECRYPT_MODE, skeySpec);
        byte[] result = cipher.doFinal(Base64.getDecoder().decode(data));
        return new String(result); // 加密
    }

    public static String getSignData(SortedMap<Object, Object> parameters) {
        Set parametersSet = parameters.entrySet();
        Iterator it = parametersSet.iterator();
        StringBuffer signData = new StringBuffer();
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            String k = (String) entry.getKey();
            Object v = entry.getValue();

            //sign字段不参与签名组串
            if (!SIGNATURE.equals(k) && !CommonUtils.isEmpty(v)) {
                signData.append(k + "=" + v + "&");
            }

        }
        //return signData.toString();
        return CommonUtils.isEmpty(signData) ? "" : signData.toString().substring(0, signData.toString().length() - 1);
    }
}
