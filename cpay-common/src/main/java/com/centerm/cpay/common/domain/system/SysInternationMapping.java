package com.centerm.cpay.common.domain.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023-02-03
 **/
@ApiModel(value = "系统国际化映射")
@ToString
@TableName("coms_internation_mapping_n")
@Data
public class SysInternationMapping {

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "名称")
    private String lName;

    @ApiModelProperty(value = "名称类型，0：中文，1：英文，2、越南语")
    private String lType;

    @ApiModelProperty(value = "映射编码")
    private String code;

    @ApiModelProperty(value = "映射类型（0：菜单，1：字典）")
    private Integer mappingType;

    @ApiModelProperty(value = "分组键")
    private String groupKey;
}
