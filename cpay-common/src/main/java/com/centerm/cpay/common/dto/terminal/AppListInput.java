package com.centerm.cpay.common.dto.terminal;

import com.centerm.cpay.common.domain.terminal.TerminalInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.miser.common.core.domain.BaseInput;

import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * 应用查询参数
 *
 * <AUTHOR>
 * @date 2020/02/20 17:40
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class AppListInput extends BaseInput<TerminalInfo> implements Serializable  {

    @ApiModelProperty(value = "页数，从1开始", required = true)
    @Min(1)
    private Integer pageNum;

    @ApiModelProperty(value = "每页显示记录数", required = true)
    @Min(1)
    private Integer pageSize;

    @ApiModelProperty(value = "关键词")
    private String keyWord;

    @ApiModelProperty(value = "应用类型ID")
    private Integer typeId;

}
