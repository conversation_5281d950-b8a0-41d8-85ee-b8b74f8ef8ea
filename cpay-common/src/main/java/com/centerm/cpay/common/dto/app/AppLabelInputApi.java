package com.centerm.cpay.common.dto.app;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: cpay
 * @description:
 * @author: Jason
 * @create: 2019/12/6
 **/
@Data
public class AppLabelInputApi extends AppLabelApi implements Serializable {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "开发者ID")
    private String devId;

    @ApiModelProperty(value = "标签名称")
    private String labelName;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "机构号")
    private Long deptId;
}
