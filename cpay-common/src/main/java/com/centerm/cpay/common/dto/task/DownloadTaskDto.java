package com.centerm.cpay.common.dto.task;

import com.centerm.cpay.common.domain.task.DownloadTask;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.miser.common.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 下载任务输出参数
 *
 * <AUTHOR>
 * @date 2019/11/27 17:10
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class DownloadTaskDto extends DownloadTask implements Serializable {

    private Long taskId;

    @Excel(name = "App Package/Log Path")
    private String appCode;

    @Excel(name = "App Name")
    private String appName;

    @Excel(name = "Job Name")
    private String jobName;


    @ApiModelProperty(value = "是否显示通知(0:否;1:是)")
    private Integer showNotify;

    @ApiModelProperty(value = "是否空闲更新(0:否;1:是)")
    private Integer isRealTime;


    @ApiModelProperty(value = "操作类型")
    private Integer actionType;


    @ApiModelProperty(value = "内部版本")
    private String versionCode;

    @ApiModelProperty(value = "应用版本")
    private String versionName;

    @ApiModelProperty(value = "文件大小")
    private String appSize;


    @ApiModelProperty(value = "应用类型")
    private Integer appType;


    @ApiModelProperty(value = "文件地址")
    private String appPath;

    @ApiModelProperty(value = "文件md5")
    private String fileMd5;

    private Date validStartTime;
    private Date validEndTime;
}
