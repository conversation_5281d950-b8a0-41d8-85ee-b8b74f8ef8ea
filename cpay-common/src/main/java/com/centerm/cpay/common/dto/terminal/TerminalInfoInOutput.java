package com.centerm.cpay.common.dto.terminal;

import com.centerm.cpay.common.domain.terminal.TerminalInfo;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 终端信息对象扩展信息
 *
 * @author: <PERSON>
 * @create: 2019/10/25
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class TerminalInfoInOutput extends TerminalInfo implements Serializable {


    @ApiModelProperty(value = "终端组别id")
    private Long groupId;

    @ApiModelProperty(value = "终端类型code集合")
    private String terminalTypeCodes;

    @ApiModelProperty(value = "终端类型id集合")
    private List<Long> terminalTypeIds;

}
