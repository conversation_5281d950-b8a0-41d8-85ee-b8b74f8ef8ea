package com.centerm.cpay.common.dto.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 软件版本信息
 *
 * <AUTHOR>
 * @date 2019-10-16
 */
@Data
public class SoftVersionInOutput implements Serializable {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "软件信息id")
    private Long appId;

    @ApiModelProperty(value = "软件版本号")
    private String appVersion;

    @ApiModelProperty(value = "软件大小")
    private Integer appSize;

    @ApiModelProperty(value = "软件路径")
    private String appPath;

    @ApiModelProperty(value = "图标文件")
    private String iconPath;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "文件md5")
    private String fileMd5;

    @ApiModelProperty(value = "机构号")
    private Long deptId;

    @ApiModelProperty(value = "厂商id")
    private Integer firmId;

    @ApiModelProperty(value = "型号ID")
    private Integer termTypeId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
