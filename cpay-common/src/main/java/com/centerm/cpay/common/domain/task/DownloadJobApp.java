package com.centerm.cpay.common.domain.task;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 任务下载软件信息
 *
 * <AUTHOR>
 * @date 2019/10/22 15:40
 **/
@Data
public class DownloadJobApp implements Serializable {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "应用ID")
    private Long appId;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "应用编号")
    private String appCode;

    @ApiModelProperty(value = "软件类型")
    private Integer appType;

    @ApiModelProperty(value = "类型：0软件表；1：应用表； 2：终端应用表")
    private Integer idType;

    @ApiModelProperty(value = "任务ID")
    private Long jobId;

    @ApiModelProperty(value = "开放平台应用ID")
    private Long copsAppId;

    @ApiModelProperty(value = "应用版本ID")
    private Long appVersionId;

    @ApiModelProperty(value = "内部版本")
    private String versionCode;

    @ApiModelProperty(value = "应用版本")
    private String versionName;

    @ApiModelProperty(value = "操作类型")
    private Integer actionType;

    @ApiModelProperty(value = "软件大小")
    private Integer appSize;

    @ApiModelProperty(value = "软件路径")
    private String appPath;

    @ApiModelProperty(value = "文件md5")
    private String fileMd5;

    @TableField(exist = false)
    private List<SoftVersion> softVersionList;

    @TableField(exist = false)
    private List<SoftInfo> softInfoList;
}
