package com.centerm.cpay.common.dto.terminal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> xiarp
 * @description :
 * @date : 2020/1/8
 */
@Data
@Builder
public class PositionOutput implements Serializable {
    @ApiModelProperty(value = "锁机状态 0-不锁机 1-锁机 2-不操作")
    private Integer lockStatus;

    @ApiModelProperty(value = "锁机类型 1-锁终端 2-锁应用")
    private String lockType;
}
