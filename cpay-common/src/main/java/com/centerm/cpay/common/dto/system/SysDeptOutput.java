package com.centerm.cpay.common.dto.system;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;


/**
 * 机构表 sys_SysDept
 *
 * <AUTHOR>
 * @date 2018-10-07 00:01:33
 */
@ToString
@Data
@ApiModel(description = "机构参数【长度限制】")
public class SysDeptOutput implements Serializable {

    @ApiModelProperty(value = "机构id")
    private Long deptId;

    @ApiModelProperty(value = "父机构id【40】")
    private Long parentId;

    @ApiModelProperty(value = "机构号【40】")
    private String code;

    @ApiModelProperty(value = "ancestors+自己的id【1~40】")
    private String ancestors;

    @ApiModelProperty(value = "机构名称【1~80】")
    private String deptName;

    @ApiModelProperty(value = "机构状态（0正常 1停用 2删除），默认0")
    private Integer status;
}
