package com.centerm.cpay.common.dto.app;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 广告是否停用参数
 *
 * <AUTHOR>
 * @date 2019/10/28
 **/
@Data
public class AdvertIsDisabled implements Serializable {

    @ApiModelProperty(value = "广告ID")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "是否停用")
    @NotNull
    private Boolean isDisabled;
}
