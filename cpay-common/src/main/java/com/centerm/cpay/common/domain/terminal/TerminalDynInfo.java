package com.centerm.cpay.common.domain.terminal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.miser.common.annotation.Excel;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 终端动态信息对象 coms_terminal_dyn_info
 *
 * <AUTHOR>
 * @date 2019-11-28
 */
@ApiModel(value = "终端动态信息")
@Data
@ToString
@TableName("coms_terminal_dyn_info")
@Accessors(chain = true)
public class TerminalDynInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "${comment}")
    private Long id;

    /**
     * 机构id
     */
    @Excel(name = "机构id")
    @ApiModelProperty(value = "机构id")
    private Long deptId;

    /**
     * 终端sn号
     */
    @Excel(name = "终端sn号")
    @ApiModelProperty(value = "终端sn号")
    private String tusn;

    /**
     * 硬件状态位图
     */
    @Excel(name = "硬件状态位图")
    @ApiModelProperty(value = "硬件状态位图")
    private String hardwareStatBmp;

    /**
     * 锁机状态
     */
    @Excel(name = "锁机状态",readConverterExp = "0=未锁机,1=锁机,2=解锁中,3=锁机中")
    @ApiModelProperty(value = "锁机状态 0-未锁机 1-锁机 2-解锁中,3-锁机中")
    private Integer lockStatus;

    /**
     * 告警信息
     */
    @Excel(name = "告警信息")
    @ApiModelProperty(value = "告警信息")
    private String expInfoList;

    /**
     * 最近通讯时间
     */
    @Excel(name = "最近通讯时间")
    @ApiModelProperty(value = "最近通讯时间")
    private String latestAccessTime;

    /**
     * 状态上送时间
     */
    @Excel(name = "状态上送时间")
    @ApiModelProperty(value = "状态上送时间")
    private String recordUploadTime;

    /**
     * 基站信息
     */
    @Excel(name = "基站信息")
    @ApiModelProperty(value = "基站信息")
    private String baseInfo;

    /**
     * ip端口号
     */
    @Excel(name = "ip端口号")
    @ApiModelProperty(value = "ip端口号")
    private String ipPort;

    /**
     * 参数版本号
     */
    @Excel(name = "参数版本号")
    @ApiModelProperty(value = "参数版本号")
    private String paraVersion;

    @Excel(name = "经度")
    @ApiModelProperty(value = "经度")
    private String longitude;

    @Excel(name = "纬度")
    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "地址")
    private String address;

    private Date createTime;
    private Date updateTime;

    @TableField(exist = false)
    private Date beginTime;
    @TableField(exist = false)
    private Date endTime;

    @TableField(exist = false)
    private List<String> tusns;
}
