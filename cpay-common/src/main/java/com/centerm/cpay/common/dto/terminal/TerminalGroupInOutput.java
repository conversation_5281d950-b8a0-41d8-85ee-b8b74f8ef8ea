package com.centerm.cpay.common.dto.terminal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: cpay
 * @description: 终端组别信息查询
 * @author: <PERSON>
 * @create: 2019/12/6
 **/
@Data
public class TerminalGroupInOutput implements Serializable {

    @ApiModelProperty(value = "组别id")
    private Long id;

    @ApiModelProperty(value = "终端组别")
    private String name;

    @ApiModelProperty(value = "机构号")
    private Long deptId;

}
