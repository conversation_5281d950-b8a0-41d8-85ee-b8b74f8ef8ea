package com.centerm.cpay.common.domain.terminal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.centerm.cpay.common.dto.base.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;
import org.miser.common.annotation.Excel;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 终端信息对象 coms_terminal_info
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "终端信息")
@Data
@ToString
@TableName("coms_terminal_info")
@Accessors(chain = true)
public class TerminalInfo extends CommonEntity implements Serializable {

    @ApiModelProperty(value = "终端id")
    @TableId
    private Long id;

    @Excel(name = "终端序列号")
    @ApiModelProperty(value = "终端序列号")
    @NotBlank(message = "终端序列号不能为空")
    @Length(max = 50, message = "终端序列号不能超过50")
    private String tusn;

    @ApiModelProperty(value = "厂商编码")
    private Long firmId;

    @ApiModelProperty(value = "厂商名称")
    private String firmName;

    @TableField(exist = false)
    @Excel(name = "厂商编码")
    @ApiModelProperty(value = "厂商编码")
    private String firmCode;

    @ApiModelProperty(value = "终端型号编码")
    private Long terminalTypeId;

    @Excel(name = "终端型号名称")
    @ApiModelProperty(value = "终端型号名称")
    private String terminalTypeName;

    @ApiModelProperty(value = "imei")
    private String imei;

    @ApiModelProperty(value = "imsi")
    private String imsi;

    @ApiModelProperty(value = "iccid")
    private String iccid;

    @ApiModelProperty(value = "3g imsi/wifi mac address")
    private String netMark;

    @Excel(name = "状态", readConverterExp = "0=已接入,1=未接入,2=移机")
    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "初始化经度")
    private String longitude;

    @ApiModelProperty(value = "初始化纬度")
    private String latitude;

    @Excel(name = "组别名称")
    @ApiModelProperty(value = "组别名称")
    @TableField(exist = false)
    private String groupNames;

    //@Excel(name = "组别id")
    @ApiModelProperty(value = "组别id")
    @TableField(exist = false)
    private Long terminalGroupId;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "网络状态 0：离线，1：在线")
    private String networkStatus;

    @ApiModelProperty(value = "蓝牙状态 0：关闭，1：启动")
    private String bluetoothStatus;

    @ApiModelProperty(value = "终端NFC状态 0：关闭，1：启动")
    private String nfcStatus;

    @ApiModelProperty(value = "终端充电状态 0：未充电，1：充电")
    private String chargingStatus;

    @ApiModelProperty(value = "终端存储情况(用/分割)")
    private String storage;

    @ApiModelProperty(value = "iccid副卡")
    private String iccid2;

    @ApiModelProperty(value = "imei副卡")
    private String imei2;

    @ApiModelProperty(value = "imsi副卡")
    private String imsi2;

    @ApiModelProperty(value = "运营商信息")
    private String carrier;

    @ApiModelProperty(value = "开机时间(分钟)")
    private String bootTime;

    @ApiModelProperty(value = "系统语言")
    private String systemLanguage;

    @ApiModelProperty(value = "系统时区")
    private String systemTimezone;

    @ApiModelProperty(value = "CPU使用量，单位百分比")
    private String cpuUsage;

    @ApiModelProperty(value = "内存使用量，单位百分比")
    private String ramUsage;

    @ApiModelProperty(value = "电量使用量，单位百分比")
    private String batteryUsage;
}