package com.centerm.cpay.common.dto.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.miser.common.annotation.Excel;
import org.miser.common.core.domain.BaseInput;

import java.io.Serializable;

/**
 * 终端故障申报请求参数
 *
 * <AUTHOR>
 * @date 2020/04/09 16:49
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class FaultUploadInput extends BaseInput implements Serializable {

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String linkPhone;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String linkMan;

    /**
     * 故障类型
     */
    @Excel(name = "故障类型")
    @ApiModelProperty(value = "故障类型")
    private String faultType;

    /**
     * 故障描述
     */
    @ApiModelProperty(value = "故障描述")
    private String faultMessage;

}