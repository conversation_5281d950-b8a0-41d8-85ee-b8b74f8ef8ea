package com.centerm.cpay.common.dto.terminal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> xiarp
 * @description :
 * @date : 2020/1/31
 */
@Data
public class StrategyScopeInput implements Serializable {
    @ApiModelProperty(value = "策略ID")
    private long strategyId;
    @ApiModelProperty(value = "适用范围 1-机构  2-终端列表")
    private Integer scopeType;
    @ApiModelProperty(value = "策略适用机构ID列表")
    private Long[] deptIds;
    @ApiModelProperty(value = "策略适用tusn列表")
    private String tusns;
}
