package com.centerm.cpay.common.dto.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 任务下载软件信息
 *
 * <AUTHOR>
 * @date 2019/10/22 15:40
 **/
@Data
public class ApiDownloadJobApp implements Serializable {

    @ApiModelProperty(value = "应用ID")
    private Long appId;

    @ApiModelProperty(value = "操作类型")
    private Integer actionType;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "icon文件存储路径")
    private String iconPath;

    @ApiModelProperty(value = "应用编号")
    private String appCode;

    @ApiModelProperty(value = "软件类型")
    private Integer appType;

    @ApiModelProperty(value = "类型：0软件表；1：应用表； 2：终端应用表")
    private Integer idType;

    @ApiModelProperty(value = "内部版本")
    private String versionCode;

    @ApiModelProperty(value = "应用版本")
    private String versionName;

    @ApiModelProperty(value = "软件大小")
    private Integer appSize;

    @ApiModelProperty(value = "软件路径")
    private String appPath;

    @ApiModelProperty(value = "文件md5")
    private String fileMd5;

}
