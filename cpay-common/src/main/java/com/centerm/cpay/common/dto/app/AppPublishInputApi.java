package com.centerm.cpay.common.dto.app;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 应用发布参数
 *
 * <AUTHOR>
 * @date 2019/09/29 18:02
 **/
@Data
public class AppPublishInputApi implements Serializable {

    @ApiModelProperty(value = "应用id")
    @NotNull
    private Long appId;

    @ApiModelProperty(value = "是否上架，false：下架")
    @NotNull
    private Boolean isOnShelves;

    @ApiModelProperty(value = "推荐度")
    private Integer reDegree;

    @ApiModelProperty(value = "是否可卸载：1：可卸载")
    private Integer isUninstall;

}
