package com.centerm.cpay.common.dto.terminal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/***
 * 终端位置信息
 *
 * <AUTHOR>
 * @date 2019/12/04
 **/
@Data
public class TerminalFlowMonthInOutput implements Serializable {

    @ApiModelProperty(value = "主键id", hidden = true)
    private Long id;

    @ApiModelProperty(value = "终端序列号")
    private String tusn;

    @ApiModelProperty(value = "统计流量年月")
    private String recordMonth;

    @ApiModelProperty(value = "月统计流量")
    private Long monthFlow;

    @ApiModelProperty(value = "创建时间", hidden = true)
    private Date createTime;

    @ApiModelProperty(value = "更新时间", hidden = true)
    private Date updateTime;
}
