package com.centerm.cpay.common.domain.terminal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.miser.common.annotation.Excel;
import org.miser.common.core.domain.BaseEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * 终端库存对象 coms_terminal_warehouse
 *
 * <AUTHOR> chong
 * @date 2020-04-09
 */
@ApiModel(value = "终端库存对象")
@Data
@ToString
@TableName("coms_terminal_warehouse")
@Accessors(chain = true)
public class TerminalWarehouse extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 编号ID
     */
    @ApiModelProperty(value = "${comment}")
    private Integer id;

    /**
     * 终端序列号
     */
    @Excel(name = "终端序列号")
    @ApiModelProperty(value = "终端序列号")
    private String tusn;

    /**
     * 终端厂商
     */
    @Excel(name = "终端厂商")
    @ApiModelProperty(value = "终端厂商")
    private String termMfrCode;

    /**
     * 终端型号
     */
    @Excel(name = "终端型号")
    @ApiModelProperty(value = "终端型号")
    private String termTypeCode;

    /**
     * 入库人姓名
     */
    @Excel(name = "入库人姓名")
    @ApiModelProperty(value = "入库人姓名")
    private String inStorageLinkman;

    /**
     * 入库时间
     */
    @Excel(name = "入库时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "入库时间")
    private Date inStorageDate;

    /**
     * 入库人联系方式
     */
    @Excel(name = "入库人联系方式")
    @ApiModelProperty(value = "入库人联系方式")
    private String inStorageLinkphone;

    /**
     * 出库人姓名
     */
    @Excel(name = "出库人姓名")
    @ApiModelProperty(value = "出库人姓名")
    private String outStorageLinkman;

    /**
     * 出库人联系方式
     */
    @Excel(name = "出库人联系方式")
    @ApiModelProperty(value = "出库人联系方式")
    private String outStorageLinkphone;

    /**
     * 出库时间
     */
    @Excel(name = "出库时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "出库时间")
    private Date outStorageDate;

    /**
     * 领用人姓名
     */
    @Excel(name = "领用人姓名")
    @ApiModelProperty(value = "领用人姓名")
    private String receiveLinkman;

    /**
     * 领用人联系方式
     */
    @Excel(name = "领用人联系方式")
    @ApiModelProperty(value = "领用人联系方式")
    private String receiveLinkphone;

    /**
     * 所属机构ID
     */
    @Excel(name = "所属机构ID")
    @ApiModelProperty(value = "所属机构ID")
    private Long deptId;

    /**
     * 库存状态
     */
    @Excel(name = "库存状态")
    @ApiModelProperty(value = "库存状态")
    private String termStatus;

    /**
     * 终端状态
     */
    @Excel(name = "终端状态")
    @ApiModelProperty(value = "终端状态")
    private String termRealStatus;

    /**
     * 报废时间
     */
    @Excel(name = "报废时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "报废时间")
    private Date bfDate;

    /**
     * 报废时间
     */
    @ApiModelProperty(value = "所属机构")
    @TableField(exist = false)
    private String deptName;

}
