package com.centerm.cpay.common.dto.terminal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/***
 * 获取终端详情信息
 *
 * <AUTHOR>
 * @date 2019/9/18 19:17
 **/
@Data
public class TerminalSysdetailOutput implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "终端序列号")
    private String tusn;

    @ApiModelProperty(value = "系统版本详情")
    private String sysDetail;

    @ApiModelProperty(value = "通信参数版本")
    private String commParaVersion;

    @ApiModelProperty(value = "Launcher参数版本")
    private String launcherParaVersion;

    @ApiModelProperty(value = "操作系统版本")
    private String osVersion;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}