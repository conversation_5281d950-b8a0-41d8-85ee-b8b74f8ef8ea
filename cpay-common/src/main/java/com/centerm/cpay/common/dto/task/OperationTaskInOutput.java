package com.centerm.cpay.common.dto.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 终端操作详细对象 coms_operation_task
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
@Data
public class OperationTaskInOutput implements Serializable {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "任务id")
    private Long jobId;

    @ApiModelProperty(value = "终端序列号")
    private String tusn;

    @ApiModelProperty(value = "操作状态")
    private Integer status;

    @ApiModelProperty(value = "操作结果说明")
    private String resultMsg;

    @ApiModelProperty(value = "操作结果说明")
    private Date operatorTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}


