package com.centerm.cpay.common.dto.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 日志文件信息
 *
 * <AUTHOR>
 * @date 2019/11/15 11:45
 **/

@Data
public class LogFileInfo implements Serializable {


    @ApiModelProperty(value = "任务ID")
    @NotNull
    private Long taskId;

    @ApiModelProperty(value = "fileMd5")
    @NotBlank
    private String fileMd5;

    @ApiModelProperty(value = "终端SN号")
    private String termTusn;

    @ApiModelProperty(value = "版本号")
    private String version;

    @ApiModelProperty(value = "签名")
    private String sign;


}
