package com.centerm.cpay.common.enums.task;

/**
 * 软件任务下载状态
 *
 * <AUTHOR>
 * @date 2019/10/21 15:48
 **/
public enum DownloadStatusEnum {
    /**
     * 等待下发
     **/
    TO_RELEASE(1),
    /**
     * 取消下发
     **/
    RELEASE_CANCEL(0),
    /**
     * 下发成功
     **/
    RELEASE_SUCCESS(2),
    /**
     * 下载成功
     **/
    DOWNLOAD_SUCCESS(3),
    /**
     * 更新成功
     **/
    UPDATE_SUCCESS(4),

    /**
     * 卸载成功
     **/
    UNINSTALL_SUCCESS(5),
    /**
     * 下载失败
     **/
    DOWNLOAD_FAIL(6),

    /**
     * 更新失败
     **/
    UPDATE_FAIL(7),
    /**
     * 卸载失败
     **/
    UNINSTALL_FAIL(8),
    /**
     * 待发布
     **/
    TO_PUBLISH(9);

    private int code;

    DownloadStatusEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return this.code;
    }

}
