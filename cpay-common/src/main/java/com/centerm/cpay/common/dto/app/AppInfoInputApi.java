package com.centerm.cpay.common.dto.app;


import com.centerm.cpay.common.dto.base.ApiBaseInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: cpay
 * @description:
 * @author: <PERSON>  Modified by <PERSON>
 * @create: 2019/10/11 16:43
 **/
@Data
public class AppInfoInputApi extends ApiBaseInput implements Serializable {
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "应用编号")
    private String apkCode;

    @ApiModelProperty(value = "应用名称")
    private String apkName;

    @ApiModelProperty(value = "应用版本号")
    private Long versionCode;

    @ApiModelProperty(value = "外部版本号")
    private String versionName;

    @ApiModelProperty(value = "应用标签名称")
    private String labelName;

    @ApiModelProperty(value = "应用标签id")
    private String labels;

    @ApiModelProperty(value = "终端组别id")
    private Long groupId;

    @ApiModelProperty(value = "终端类型ids")
    private String terminalTypes;

    @ApiModelProperty(value = "查询终端类型id")
    private String termTypeId;

    @ApiModelProperty(value = "应用类型id")
    private Long typeId;

    @ApiModelProperty(value = "终端厂商id")
    private Long firmId;

    @ApiModelProperty(value = "推荐度")
    private Integer reDegree;

    @ApiModelProperty(value = "是否可卸载")
    private Integer isUninstall;

    @ApiModelProperty(value = "是否通用")
    private Integer isUniversal;

    @ApiModelProperty(value = "应用状态")
    private Integer status;

    @ApiModelProperty(value = "应用状态列表")
    private Integer[] statusList = {};

    private Integer signType;

    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "应用推广状态  0:正常  1:预装 2:推荐")
    private Integer promoteStatus;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    private String updateBy;
}
