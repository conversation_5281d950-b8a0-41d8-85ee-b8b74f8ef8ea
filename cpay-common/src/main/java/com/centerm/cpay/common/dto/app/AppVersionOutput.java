package com.centerm.cpay.common.dto.app;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 应用版本列表
 *
 * <AUTHOR>
 * @date 2019/10/24 11:39
 **/
@Data
public class AppVersionOutput implements Serializable {

    @ApiModelProperty(value = "应用ID")
    private Long id;

    @ApiModelProperty(value = "版本描述")
    private String versionDescription;

    @ApiModelProperty(value = "应用编号")
    private String apkCode;

    @ApiModelProperty(value = "应用名称")
    private String apkName;

    @ApiModelProperty(value = "应用版本号")
    private Long versionCode;

    @ApiModelProperty(value = "外部版本号")
    private String versionName;

}
