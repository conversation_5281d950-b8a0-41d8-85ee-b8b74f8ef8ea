package com.centerm.cpay.common.dto.task;

import com.centerm.cpay.common.dto.base.ApiBaseInput;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * @program: cpay
 * @description:
 * @author: <PERSON>
 * @create: 2019/10/23 17:26
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PublishJobInputApi extends ApiBaseInput implements Serializable {
    @ApiModelProperty(value = "终端列表")
    private List<String> terminalList;

    @ApiModelProperty(value = "发布类型 0-条件 1-机构")
    private Integer releaseType;

    @ApiModelProperty(value = "发布组别id")
    private Integer groupId;
}
