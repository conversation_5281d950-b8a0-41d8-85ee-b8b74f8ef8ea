package com.centerm.cpay.common.dto.app;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 广告审核参数
 *
 * <AUTHOR>
 * @date 2019/10/28
 **/
@Data
public class AdvertAuditApi implements Serializable {

    @ApiModelProperty(value = "广告ID")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "审核人")
    private String reviewUser;

    @ApiModelProperty(value = "审核时间")
    private Date reviewTime;

    @ApiModelProperty(value = "审核信息")
    private String reviewMsg;

    @ApiModelProperty(value = "是否通过")
    @NotNull
    private Boolean isPass;
}
