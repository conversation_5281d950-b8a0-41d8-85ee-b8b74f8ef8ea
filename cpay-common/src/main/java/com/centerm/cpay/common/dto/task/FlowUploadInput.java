package com.centerm.cpay.common.dto.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.miser.common.core.domain.BaseInput;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 流量上送请求参数
 *
 * <AUTHOR>
 * @date 2019/11/15 11:25
 **/

@EqualsAndHashCode(callSuper = true)
@Data
public class FlowUploadInput extends BaseInput implements Serializable {

    @ApiModelProperty(value = "流量信息列表")
    @NotEmpty
    private List<FlowInfo> flowInfoList;

    @Data
    public static class FlowInfo implements Serializable {

        @ApiModelProperty(value = "统计天（yyyyMMdd）")
        @NotBlank
        private String recordDate;

        @ApiModelProperty(value = "统计时间(HHmmss)")
        @NotBlank
        private String recordTime;

        @ApiModelProperty(value = "统计流量")
        @NotBlank
        private String countFlow;

    }

}
