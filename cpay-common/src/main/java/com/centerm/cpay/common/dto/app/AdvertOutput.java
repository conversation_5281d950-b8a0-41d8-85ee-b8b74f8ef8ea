package com.centerm.cpay.common.dto.app;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 广告查询响应结果
 *
 **/
@Data
public class AdvertOutput implements Serializable {

    @ApiModelProperty(value = "广告id")
    private Long id;

    @ApiModelProperty(value = "广告类型 Notification Popup:1、App Store Banner:2、Launcher Background Image:3、Device Boot Animation:4")
    private Integer type;

    @ApiModelProperty(value = "广告名称")
    private String adName;

    @ApiModelProperty(value = "图片地址")
    private String picPath;

    @ApiModelProperty(value = "广告描述")
    private String description;
}
