package com.centerm.cpay.common.domain.task;

import com.baomidou.mybatisplus.annotation.TableField;
import com.centerm.cpay.common.domain.app.AdvertInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.miser.common.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告任务对象扩展信息
 *
 **/
@Data
public class AdvertJobInOutput extends AdvertJob implements Serializable {


    @Excel(name = "所属机构")
    @ApiModelProperty(value = "所属机构")
    private String deptName;

    @Excel(name = "分组名称")
    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "起始起始时间")
    private Date beginStartTime;

    @ApiModelProperty(value = "结束起始时间")
    private Date endStartTime;

    @ApiModelProperty(value = "起始结束时间")
    private Date beginEndTime;

    @ApiModelProperty(value = "结束结束时间")
    private Date endEndTime;

    @TableField(exist = false)
    private Integer colorStyle;

}
