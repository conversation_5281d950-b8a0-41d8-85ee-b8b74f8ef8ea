package com.centerm.cpay.common.dto.task;

import com.centerm.cpay.common.dto.base.ApiBaseInput;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务基本信息对象 coms_download_job
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "任务基本信息对象")
@Data
public class ApiDownloadJob extends ApiBaseInput implements Serializable {


    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "任务名称")
    private String jobName;

    @ApiModelProperty(value = "发布类型")
    private Integer releaseType;


    @ApiModelProperty(value = "发布机构")
    private Long deptId;

    @ApiModelProperty(value = "厂商id")
    private Integer firmId;

    @ApiModelProperty(value = "发布组别id")
    private Integer groupId;

    @ApiModelProperty(value = "终端型号id")
    private String terminalTypes;

    @ApiModelProperty(value = "终端集合(用 `;`连接)")
    private String tusns;

    @ApiModelProperty(value = "是否显示通知(0:否;1:是)")
    private Integer showNotify;

    @ApiModelProperty(value = "是否空闲更新(0:否;1:是)")
    private Integer isRealTime;

    @ApiModelProperty(value = "有效起始时间")
    private Date validStartTime;

    @ApiModelProperty(value = "有效截止时间")
    private Date validEndTime;

    private String devId;
    private String remark;

}


