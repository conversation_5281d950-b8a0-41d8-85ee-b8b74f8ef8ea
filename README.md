## 平台简介
CPAY管理平台框架。
## 目录结构
```

├─cpay4                     #   运维系统
│  ├─cpay-admin             #   运维系统coms服务接口
│  ├─cpay-common            #   运维系统公共模块
│  ├─cpay-config            #   运维系统参数配置
│  ├─cpay-gateway           #   运维系统网关服务
│  │   ├─cpay-ams           #   ams应用商店网关
│  │   ├─cpay-tms           #   tms网关
│  └─cpay-service           #   运维系统service层
│      ├─cpay-app           #   app应用service
│      ├─cpay-task          #   远程运维任务service
│      ├─cpay-terminal      #   终端相关service
│      ├─cpay-emq-service   #   emq核心组件
│      ├─cpay-msgcenter     #   emq管理组件


```
## 前端模块
```
1.引入选择机构模块
  机构输入  <th:block th:include="modules :: dept-select-view"/>
  机构组别输入 <th:block th:include="modules :: dept-group-input-view"/>
2.引入导入功能模块
    导入界面  <th:block th:include="modules :: import-form-view"/>
    导入结果  <th:block th:modules="modules :: import-result-view"/> 
    onclick="$.table.importExcel(tableId)" 
3.终端厂商型号
   <th:block th:include="modules :: firm_type_view"/>
   <th:block th:include="modules :: select2-js"/>
4、中英文切换
`````
## 后端结构
`````
1.查询列表显示机构名称

   BaseVo  自动查找缓存中的机构名称注入对象
   例子：public class AppLabel extends BaseVo
2.只能查询子机构的数据

  在 **mapper.java 的查询方法上加上注解 @permissionData
  自动注入机构权限语句

3.只能操作子机构的数据

  serviceImpl继承 BaseServiceImpl   
  方法 deleteByIdsInPermission：删除语句并注入机构权限语句
  方法 updateByIdInPermission：修改语句并注入机构权限语句

4.新增修改自动添加数据
    新增：自动填充 createBy createTime
    修改：自动填充 updateBy updateTime

5.文件服务,解析apk等文件服务

 IFileService

6.APP接口自动验签和加签名

依赖：jmiser-app
{"body":{},"header":{"devMask":"","imei":"","netMark":"","random":"","timestamp":"","version":""},"sign":""}
接口取消自动(controller)：@NoRequestSecurity
全部不加密(yml)：app.isSign:false

````
## 内置功能

1.  用户管理：用户是系统操作者，该功能主要完成系统用户配置。
2.  机构管理：配置系统组织机构（公司、机构、小组），树结构展现。
3.  岗位管理：配置系统用户所属担任职务。
4.  菜单管理：配置系统菜单，操作权限，按钮权限标识等。
5.  角色管理：角色菜单权限分配、设置角色按机构进行数据范围权限划分。
6.  字典管理：对系统中经常使用的一些较为固定的数据进行维护。
7.  参数管理：对系统动态配置常用参数。
8.  通知公告：系统通知公告信息发布维护。
9.  操作日志：系统正常操作日志记录和查询；系统异常信息日志记录和查询。
10. 登录日志：系统登录日志记录查询包含登录异常。
11. 在线用户：当前系统中活跃用户状态监控。
12. 定时任务：在线（添加、修改、删除)任务调度包含执行结果日志。
13. 代码生成：前后端代码的生成（java、html、xml、sql)支持CRUD下载 。
14. 系统接口：根据业务代码自动生成相关的api接口文档。
15. 在线构建器：拖动表单元素生成相应的HTML代码。
16. 连接池监视：监视当期系统数据库连接池状态，可进行分析SQL找出系统性能瓶颈。

## 平台启动
1. 修改环境参数配置文件：cpay/cpay-admin/src/main/resources/config/application.properties
2. 在命令行用maven启动：mvn -pl cpay-admin spring-boot:run
3. 在IDEA或Eclipse工具中用Run程序启动。
4. 访问地址：http://localhost:8080，默认登录密码admin/

## 环境参数
1. 如果开发、测试或生产环境参数不一样，可在cpay/cpay-admin/src/main/resources/config目录增加对应的参数配置文件。如：application-dev.properties、application-test.properties、application-prod.properties
2. 在命令行用maven启动（带参数）：mvn -pl cpay-admin spring-boot:run -Dspring-boot.run.profiles=dev,druid
3. 在IDEA或Eclipse工具中配置Run程序的Active profiles=dev,druid参数，再执行Run程序启动。
4. 修改应用版本号mvn versions:set -DnewVersion=1.0.0


## 通讯密钥
##client
Public Key (Base64):
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuc38eNP815Q9kOqCZ2qy6hIHJE1XUWIPtHqVHfmE3avKxFI3tuZ6ZRp98u9tzCTwYT5aRkdHTqgREO25DXA4hTfoQUZoBxfqrRf+6dH2yZG+4kmdE7jYkZj4qKlh8i+l5jQXNtUdLcD8s7QfQbNWwzw18K6PipEX8W6RYik34i8AwquDK2loVcpdWAn3GZtfRyCOrbFkvQBVGnQLGf/YpwQhO6MUtM15eO0CHTU7tRAy8r1iWiUdWTlOny3q5ZuxwvwdEFHjLbnCshko2LX5Usz8IWL2owLACSm249/k9Z0HmC7vEZom5SyukDkkEBR7q7+A3O7e6eSwAos820QurwIDAQAB
Private Key (Base64):
MIIEwAIBADANBgkqhkiG9w0BAQEFAASCBKowggSmAgEAAoIBAQC5zfx40/zXlD2Q6oJnarLqEgckTVdRYg+0epUd+YTdq8rEUje25nplGn3y723MJPBhPlpGR0dOqBEQ7bkNcDiFN+hBRmgHF+qtF/7p0fbJkb7iSZ0TuNiRmPioqWHyL6XmNBc21R0twPyztB9Bs1bDPDXwro+KkRfxbpFiKTfiLwDCq4MraWhVyl1YCfcZm19HII6tsWS9AFUadAsZ/9inBCE7oxS0zXl47QIdNTu1EDLyvWJaJR1ZOU6fLerlm7HC/B0QUeMtucKyGSjYtflSzPwhYvajAsAJKbbj3+T1nQeYLu8RmiblLK6QOSQQFHurv4Dc7t7p5LACizzbRC6vAgMBAAECggEBAJa/nTubIuT1jQ3qdmrZktd+UPUPGu0Ylo1jd2CedUI0g4rM+g8W5+7nk5Bq2bZrsevz3uJuKliS22XUufNBPVo4heOP8A7xRKGzekrS41ufODP4bcz2iB948SZGZwVLy1n+oWcHeWkJzv0uAr4XdylnzIu91RAjsD1d6+6san7DKe6Fgg7V8Lkro9zlQRHzYLAhtpNFXoYCTmlmT66uKnLplBlSl0Xs6ZF9TWuY6xpxj2uGD+SmYT+Vc9hK7gNI6dUIDM1ZKcSgGi3bAcVHaauMfnRCcgb9rJnWJ7vAFUuyhvxCkpgWYslKQFvQJdRNwAQR90ciYfPsU/Omy5HEriECgYEA3nNNd1bDf/M5F4NPMGUOcefH825SIjhw97m6v3BJjvScVUOUcoSjm9M+Mzi7g5togQhKouo8Uo1cTI22Qlr4W8fsWKo5Ali6/GTB2bech/ML6W3j8jGjYpo1pIS55qz/U8y92zkR3UyaWmoO8U4P9kCSY+Y84z50nZoVpYF06tMCgYEA1dPPIL5T6IE+fu3bMpgG4IeQp3jH/zGmM/lXjgC7MXvseL116actvTlXYYVAWuSxEJWM+vh6Wy9vS+pNUU9sj1xnK+yqFvpFK1c77Pf1Qvxj8yIceynAo/Czx7et85isZxhLPZW9chj/+i8PSV1qIE3WG4OUpcRKCZZ+G2vVizUCgYEAiLmD3prUdJsw9xO9I3iphTc5/ZRMJT5dqNVeQ/YnMBeG0x1Da5O837TZ4S+4iS6G7/ABt9qSkwbRuTfM00IIrpjV2rVdbuTMoP7o6wFg02p+Ys4pMZ5y1tMDr4fonzCrin0oIEsWgNdpc7MGVihtxSRxFbfYqq+ji46+SUilbQMCgYEArcVwct+6OR2vuF983+UE0bvaJv/VKOMkeuCL8u9NMa3QQ19VUAl49mp/RNWRs0xfSWcAJ4V/ilNGU5+aLhYp12f0wJGYuQuy4sIGBVKNk16hq5lxI/aOg6y9rfTr/Sd6FF595xKl3u/KN3u/JJQyBwa6lamdSAn5UUloOsirxnECgYEAwDspKxTT4FyqFxapSmWwP+jFl0DenbofNA1hXkXzDp9QEeiTaXKugk0ZyVGAPQjasv3v4qF8bZnlyFkB0WAnOGc7Gs/G72rKcw8j2hMhmeXnD5Us6gw3gnaeQXhVbQ9HBukVTDTxRfnio39EKdom+VBdeLem45lnSdojvc4FvRk=
##platform
Public Key (Base64):
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlTDpb7CufsDv8UYxmHB7rNa16YYzAOprjc2feCD3lbpfYUmot0QHH63yY7N9Y/tLhP7i3QmapOU9GweO/nGlIxcWFGgE1/Plce2omkUPJ8O5SmizoqcqOXxSKaZVDbxXqA4walJsbKVPrbMy0q18nZqzRNPq61mkbofvcATseLeqREoDl2h2ijCcjtpqh3jUbKTf7+jLGecROzonMcSFHEaJKHPZ4xfY13xK2vYDuY/j9gOo2diCWsH55F3FqRWxGfIZEJyVa4TcWAN6TtakVePQqPqYYEO6mA0234VVsMOeobdSysW6acfJDfqtpEQFuyq/Vf9AFJyFfG/RSxEI/QIDAQAB
Private Key (Base64):
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCVMOlvsK5+wO/xRjGYcHus1rXphjMA6muNzZ94IPeVul9hSai3RAcfrfJjs31j+0uE/uLdCZqk5T0bB47+caUjFxYUaATX8+Vx7aiaRQ8nw7lKaLOipyo5fFIpplUNvFeoDjBqUmxspU+tszLSrXydmrNE0+rrWaRuh+9wBOx4t6pESgOXaHaKMJyO2mqHeNRspN/v6MsZ5xE7OicxxIUcRokoc9njF9jXfEra9gO5j+P2A6jZ2IJawfnkXcWpFbEZ8hkQnJVrhNxYA3pO1qRV49Co+phgQ7qYDTbfhVWww56ht1LKxbppx8kN+q2kRAW7Kr9V/0AUnIV8b9FLEQj9AgMBAAECggEAE9rolla7Ql9HD6M54oHF0+DXYPbdPtfz7HFbMc+hY6nkG7yD2bASCqNJyP8QRwggqtiJNMWccSmZEknVPAO/6IWgCKM3Uc5B0QHCGJXsSGyB8mcw0QrSUcg73xMl6qgQOmt5Ag1qJKNzkThyIRu5o/f8t8JEV4IcoiExOoVNO1twrIyibVjO6u+pP36F94xpWp04/2CYXhVTLQ9+xNMAjJiFHNCAEdnmkpUi9gRZdgmiSOp+cfhu2PxCUmfSh+wvG1KrdufWmiE7wuo9A2jgo8SvPTCVb1R1vPvjcUeQcIi0kXJlAO51iB6lYxDo8hs5zX8YxDWznFjq4UN/XsH5gQKBgQD35XCFoOuhp6yf2+Fqb+/usBqd2zD3ctLCb4Y28FKmubatqaMOnIwofh55AZzgvO5jsSqnAnRuAGtKEls3hSrZcMPBusFURO5IS/2z+hjwaXA7Lejd49kFjyfWJ1C1owjz9HMr6ZpW6rVMIs/fFoABB1kiiZz0I78oPYgfMHbjLQKBgQCaEXEXSxmO+mPuKnAuduoj7j5tJpNST9tjcpLG3Hvdz048qrx1CCVq1OJh7cv5LfebjJ4WyM6ZkKoQbK+U5gY54k4EZteDEvyMDyI/U5H34bqVfAOBE+4cIcvygFmsq/Td61hGAvBhIZz7E/Tb93uzM+FlSVjZ0o8UCfHVJD+fEQKBgFOsbTudootRFnJq/H45xEnTxVgtCS0s47jfffUDT1KZcvDRRomDC8gLMev0QGiXPPwHa/ctW/FZUN2Srplh6KG2b0zv5Zu1HJzokXzzWQGlBU3kZCqpYrJ+rJqbcKGuozH8+/cBhaf61zEYuwN5C8HNIigHPSQnALnrcwvcBvUNAoGAXe7maZJDKfYleS7sqJed13xJGVlcluHTW8WeEZqZ9rmO9NQQBLaLwZ+7yoMmSm0pOvA37qJNvDkERvFM99Zq013TvUFKgkLQMUCE2+FC7MP2c29h34/cK9C7PMXeZrWMnMUBU+Y00rOgt0AKmRZBmBe4g319q4iB7XDGvfHUzFECgYEApak1Ygbz0XqpVrT+wO5GbHFcO7m0wXqK6jbejv1xkNSklLU1U681TvL+dRovY2pGVbPaVTK45PG5ddZeYdbAdN9RNB+qeOizp/miRs0XlxeWY6G53xy5N7j2WS7TKMlBRNjbGoNKtheqEI0HUgGaHNctqPooeSrGBPr4WU+amPw=

