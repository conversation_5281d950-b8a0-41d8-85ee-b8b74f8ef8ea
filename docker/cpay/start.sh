#[Authorized] Set permission for backup.sh script
chmod +x /data/cpay4/backup.sh

#[Authorized] MySQL log path access
chown 999:999 /data/cpay4/mysql/log

#[Check] If cron is installed
if ! command -v crontab &>/dev/null; then
  echo "cron is not installed. Installing cron..."
  yum install -y cronie
else
  echo "cron is already installed."
fi

# [Check] If dmidecode is installed
if ! command -v dmidecode &> /dev/null; then
    echo "dmidecode is not installed. Installing dmidecode..."
    yum install -y dmidecode
else
    echo "dmidecode is already installed."
fi

#[Pull] Docker containers
docker-compose up -d

#[Backup] MySQL database
# Check if backup.sh has been added to crontab
if ! crontab -l | grep -q "/data/cpay4/backup.sh"; then
  # Add backup.sh to crontab
  (crontab -l ; echo "0 0 * * * /data/cpay4/backup.sh") | crontab -

  # Output success message after script execution
  echo "backup.sh has been added to crontab successfully."
fi


#[Clean] Cpay logs
# Check if cleanlog.sh has been added to crontab
if ! crontab -l | grep -q "/data/cpay4/cleanlog.sh"; then
  # Add cleanlog.sh to crontab
  (crontab -l ; echo "00 08 * * * /data/cpay4/cleanlog.sh") | crontab -

  # Output success message after script execution
  echo "cleanlog.sh has been added to crontab successfully."
fi