package com.centerm.cpay.coms.controller.relocation;

import java.util.List;

import com.centerm.cpay.common.domain.terminal.TerminalMoveRecord;
import com.centerm.cpay.terminal.service.ITerminalMoveRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.miser.common.core.support.Convert;
import org.miser.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.miser.common.annotation.Log;
import org.miser.common.enums.BusinessType;
import org.miser.common.core.controller.BaseController;
import org.miser.common.core.page.TableDataInfo;

/**
 * 终端移机记录 Controller
 *
 * <AUTHOR>
 * @date 2020-01-06
 */
@Api(tags = "终端移机记录 模块")
@Controller
@RequestMapping("/relocation/record")
@Slf4j
public class TerminalMoveRecordController extends BaseController {
    private String prefix = "relocation/record";

    @Autowired
    private ITerminalMoveRecordService terminalMoveRecordService;

    @RequiresPermissions("relocation:record:view")
    @GetMapping()
    public String record() {
        return prefix + "/record";
    }


    @ApiOperation(value = " 查询终端移机记录 列表")
    @RequiresPermissions("relocation:record:list")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo<TerminalMoveRecord> list(TerminalMoveRecord terminalMoveRecord) {
        startPage();
        List<TerminalMoveRecord> list = terminalMoveRecordService.selectTerminalMoveRecordList(terminalMoveRecord);
        return getDataTable(list);
    }


    @ApiOperation(value = "导出终端移机记录 列表")
    @RequiresPermissions("relocation:record:export")
    @PostMapping("/export")
    @ResponseBody
    public String export(TerminalMoveRecord terminalMoveRecord) {
        List<TerminalMoveRecord> list = terminalMoveRecordService.selectTerminalMoveRecordList(terminalMoveRecord);
        ExcelUtil<TerminalMoveRecord> util = new ExcelUtil<TerminalMoveRecord>(TerminalMoveRecord.class);
        return util.exportExcel(list, "record");
    }


    @ApiOperation(value = "新增终端移机记录 页面")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }


    @ApiOperation(value = "新增保存终端移机记录 ")
    @RequiresPermissions("relocation:record:add")
    @Log(title = "TerminalMoveRecord ", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public Boolean addSave(TerminalMoveRecord terminalMoveRecord) {
        return rowResult(terminalMoveRecordService.save(terminalMoveRecord));
    }


    @ApiOperation(value = "修改终端移机记录 页面")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        TerminalMoveRecord terminalMoveRecord = terminalMoveRecordService.selectTerminalMoveRecordById(id);
        mmap.put("terminalMoveRecord", terminalMoveRecord);
        return prefix + "/edit";
    }


    @ApiOperation(value = "修改保存终端移机记录 ")
    @RequiresPermissions("relocation:record:edit")
    @Log(title = "TerminalMoveRecord ", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public Boolean editSave(TerminalMoveRecord terminalMoveRecord) {
        return rowResult(terminalMoveRecordService.updateById(terminalMoveRecord));
    }


    @ApiOperation(value = "删除终端移机记录 ")
    @RequiresPermissions("relocation:record:remove")
    @Log(title = "TerminalMoveRecord ", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public Boolean remove(@RequestBody String ids) {
        List<Long> idsList = Convert.toLongList(ids);
        return terminalMoveRecordService.removeByIds(idsList);
    }

    @ApiOperation(value = "锁机 ")
    @RequiresPermissions("relocation:record:lock")
    @Log(title = "TerminalMoveRecord ", businessType = BusinessType.UPDATE)
    @PostMapping("/lock")
    @ResponseBody
    public Boolean lock(String ids) {
        List<Long> idsList = Convert.toLongList(ids);
        return rowResult(terminalMoveRecordService.updateTerminalMoveRecordToLockStatusByIds(idsList));
    }

    @ApiOperation(value = "解锁 ")
    @RequiresPermissions("relocation:record:unlock")
    @Log(title = "TerminalMoveRecord ", businessType = BusinessType.UPDATE)
    @PostMapping("/unlock")
    @ResponseBody
    public Boolean unlock(String ids) {
        List<Long> idsList = Convert.toLongList(ids);
        log.info("================={}=============", idsList.size());
        return rowResult(terminalMoveRecordService.updateTerminalMoveRecordToUnlockStatusByIds(idsList));
    }
}
