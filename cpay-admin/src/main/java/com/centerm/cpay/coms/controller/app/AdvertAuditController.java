package com.centerm.cpay.coms.controller.app;

import com.centerm.cpay.common.constant.app.AdvertConstants;
import com.centerm.cpay.common.domain.task.AdvertJobInOutput;
import com.centerm.cpay.common.dto.app.AdvertAuditInput;
import com.centerm.cpay.common.dto.app.AdvertInfoInOutput;
import com.centerm.cpay.common.utils.TimeZoneUtils;
import com.centerm.cpay.task.service.IAdvertJobService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.miser.common.annotation.Log;
import org.miser.common.core.controller.BaseController;
import org.miser.common.core.page.TableDataInfo;
import org.miser.common.enums.BusinessType;
import org.miser.framework.util.ShiroUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.ZoneId;
import java.util.List;

/**
 * 广告信息Controller
 *
 */
@Api(tags = "审核广告")
@RequestMapping("/advert/audit")
@Controller
public class AdvertAuditController extends BaseController {

    private String prefix = "/advert/audit";

    @Autowired
    private IAdvertJobService advertJobService;


    @RequiresPermissions("advert:audit:auditList")
    @GetMapping()
    public String info(ModelMap mmap) {
        mmap.put("timeZone", ZoneId.systemDefault().toString());
        return prefix + "/auditList";
    }

    @ApiOperation(value = "查询广告审核列表")
    @RequiresPermissions("advert:audit:auditList")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo<AdvertInfoInOutput> list(AdvertJobInOutput advertInfoInOutput) {
        startPage();
        if(advertInfoInOutput.getBeginStartTime() != null){
            advertInfoInOutput.setBeginStartTime(TimeZoneUtils.covertZone(advertInfoInOutput.getBeginStartTime(), advertInfoInOutput.getTimeZone(), ZoneId.systemDefault().toString()));
        }
        if (advertInfoInOutput.getEndStartTime() != null) {
            advertInfoInOutput.setEndStartTime(TimeZoneUtils.covertZone(advertInfoInOutput.getEndStartTime(), advertInfoInOutput.getTimeZone(), ZoneId.systemDefault().toString()));
        }
        if (advertInfoInOutput.getBeginEndTime() != null) {
            advertInfoInOutput.setBeginEndTime(TimeZoneUtils.covertZone(advertInfoInOutput.getBeginEndTime(), advertInfoInOutput.getTimeZone(), ZoneId.systemDefault().toString()));
        }
        if (advertInfoInOutput.getEndEndTime()!= null) {
            advertInfoInOutput.setEndEndTime(TimeZoneUtils.covertZone(advertInfoInOutput.getEndEndTime(), advertInfoInOutput.getTimeZone(), ZoneId.systemDefault().toString()));
        }
        advertInfoInOutput.setStatus(AdvertConstants.STATUS_UNREVIEW);
        List<AdvertJobInOutput> list = advertJobService.selectAdvertJobList(advertInfoInOutput);
        return getDataTable(list);
    }

    @ApiOperation(value = " 审核广告页面")
    @GetMapping("/audit/{id}")
    public String audit(@PathVariable("id") Long id, ModelMap mmap) {
        validId(id);
        mmap.put("advertInfo", advertJobService.getAdvertJobById(id));
        return prefix + "/audit";
    }

    @ApiOperation(value = "审核广告")
    @RequiresPermissions("advert:info:audit")
    @Log(title = "Advert", businessType = BusinessType.AUDITED)
    @PostMapping("/audit")
    @ResponseBody
    public Boolean audit(@Validated AdvertAuditInput advertAuditInput) {
        validId(advertAuditInput.getId());
        advertAuditInput.setReviewUser(ShiroUtils.getLoginName());
        return advertJobService.auditAdvertJob(advertAuditInput);
    }

}