package com.centerm.cpay.coms.controller.relocation;

import java.util.List;

import com.centerm.cpay.common.domain.terminal.TerminalMoveStrategy;
import com.centerm.cpay.terminal.service.ITerminalMoveStrategyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.miser.common.core.support.Convert;
import org.miser.common.utils.poi.ExcelUtil;
import org.miser.framework.util.ShiroUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.miser.common.annotation.Log;
import org.miser.common.enums.BusinessType;
import org.miser.common.core.controller.BaseController;
import org.miser.common.core.page.TableDataInfo;

/**
 * 移机策略配置 Controller
 * 
 * <AUTHOR>
 * @date 2020-01-06
 */
@Api(tags = "移机策略配置 模块")
@Controller
@RequestMapping("/relocation/strategy")
public class TerminalMoveStrategyController extends BaseController {
    private String prefix = "relocation/strategy";

    @Autowired
    private ITerminalMoveStrategyService terminalMoveStrategyService;

    @RequiresPermissions("relocation:strategy:view")
    @GetMapping()
    public String strategy()
    {
        return prefix + "/strategy";
    }


    @ApiOperation(value = " 查询移机策略配置 列表")
    @RequiresPermissions("relocation:strategy:list")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo<TerminalMoveStrategy> list(TerminalMoveStrategy terminalMoveStrategy) {
        startPage();
        List<TerminalMoveStrategy> list = terminalMoveStrategyService.selectTerminalMoveStrategyList(terminalMoveStrategy);
        return getDataTable(list);
    }


    @ApiOperation(value = "导出移机策略配置 列表")
    @RequiresPermissions("relocation:strategy:export")
    @PostMapping("/export")
    @ResponseBody
    public String export(TerminalMoveStrategy terminalMoveStrategy) {
        List<TerminalMoveStrategy> list = terminalMoveStrategyService.selectTerminalMoveStrategyList(terminalMoveStrategy);
        ExcelUtil<TerminalMoveStrategy> util = new ExcelUtil<TerminalMoveStrategy>(TerminalMoveStrategy.class);
        return util.exportExcel(list, "strategy");
    }


    @ApiOperation(value = "新增移机策略配置 页面")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }


    @ApiOperation(value = "新增保存移机策略配置 ")
    @RequiresPermissions("relocation:strategy:add")
    @Log(title = "TerminalMoveStrategy ", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public Boolean addSave(TerminalMoveStrategy terminalMoveStrategy) {
        terminalMoveStrategy.setDeptId(ShiroUtils.getDeptId());
        return rowResult(terminalMoveStrategyService.save(terminalMoveStrategy));
    }


    @ApiOperation(value = "修改移机策略配置 页面")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        TerminalMoveStrategy terminalMoveStrategy = terminalMoveStrategyService.selectTerminalMoveStrategyById(id);
        mmap.put("terminalMoveStrategy", terminalMoveStrategy);
        return prefix + "/edit";
    }


    @ApiOperation(value = "修改保存移机策略配置 ")
    @RequiresPermissions("relocation:strategy:edit")
    @Log(title = "TerminalMoveStrategy ", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public Boolean editSave(TerminalMoveStrategy terminalMoveStrategy)
    {
        return rowResult(terminalMoveStrategyService.updateById(terminalMoveStrategy));
    }


    @ApiOperation(value = "删除移机策略配置 ")
    @RequiresPermissions("relocation:strategy:remove")
    @Log(title = "TerminalMoveStrategy ", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public Boolean remove(String ids) {
        return terminalMoveStrategyService.removeByIds(Convert.toLongList(ids));
    }
}
