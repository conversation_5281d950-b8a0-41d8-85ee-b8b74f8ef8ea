package com.centerm.cpay.coms.controller.terminal;

import com.centerm.cpay.common.domain.terminal.TerminalFault;
import com.centerm.cpay.common.domain.terminal.TerminalFirm;
import com.centerm.cpay.terminal.service.ITerminalFaultService;
import com.centerm.cpay.terminal.service.ITerminalFirmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.miser.common.annotation.Log;
import org.miser.common.core.controller.BaseController;
import org.miser.common.core.page.TableDataInfo;
import org.miser.common.core.support.Convert;
import org.miser.common.enums.BusinessType;
import org.miser.common.utils.data.DateUtils;
import org.miser.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 终端故障申报Controller
 *
 * <AUTHOR>
 * @date 2020-04-10
 */
@Api(tags = "终端故障申报模块")
@Controller
@RequestMapping("/terminal/fault")
public class TerminalFaultController extends BaseController {
    private String prefix = "terminal/fault";

    @Autowired
    private ITerminalFaultService terminalFaultService;
    @Autowired
    private ITerminalFirmService terminalFirmService;

    @RequiresPermissions("terminal:fault:view")
    @GetMapping()
    public String fault(ModelMap mmap) {
        mmap.put("terminalFirmList", terminalFirmService.selectTerminalFirmList(new TerminalFirm()));
        return prefix + "/fault";
    }

    @ApiOperation(value = " 查询终端故障申报列表")
    @RequiresPermissions("terminal:fault:list")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo<TerminalFault> list(TerminalFault terminalFault) {
        startPage();
        List<TerminalFault> list = terminalFaultService.selectTerminalFaultList(terminalFault);
        return getDataTable(list);
    }


    @ApiOperation(value = "导出终端故障申报列表")
    @RequiresPermissions("terminal:fault:export")
    @PostMapping("/export")
    @ResponseBody
    public String export(TerminalFault terminalFault) {
        List<TerminalFault> list = terminalFaultService.selectTerminalFaultList(terminalFault);
        ExcelUtil<TerminalFault> util = new ExcelUtil<TerminalFault>(TerminalFault.class);
        return util.exportExcel(list, "fault");
    }


    @ApiOperation(value = "新增终端故障申报页面")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }


    @ApiOperation(value = "新增保存终端故障申报")
    @RequiresPermissions("terminal:fault:add")
    @Log(title = "终端故障申报", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public Boolean addSave(TerminalFault terminalFault) {
        return rowResult(terminalFaultService.save(terminalFault));
    }

    @ApiOperation(value = "终端故障申报详情页面")
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id") Long id, ModelMap mmap) {
        TerminalFault terminalFault = terminalFaultService.selectTerminalFaultById(id);
        mmap.put("terminalFault", terminalFault);
        return prefix + "/detail";
    }

    @ApiOperation(value = "修改终端故障申报页面")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        TerminalFault terminalFault = terminalFaultService.selectTerminalFaultById(id);
        mmap.put("terminalFault", terminalFault);
        return prefix + "/edit";
    }


    @ApiOperation(value = "修改保存终端故障申报")
    @RequiresPermissions("terminal:fault:edit")
    @Log(title = "终端故障申报", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public Boolean editSave(TerminalFault terminalFault) {
        terminalFault.setDealTime(DateUtils.getNowDate());
        return rowResult(terminalFaultService.updateById(terminalFault));
    }


    @ApiOperation(value = "删除终端故障申报")
    @RequiresPermissions("terminal:fault:remove")
    @Log(title = "终端故障申报", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public Boolean remove(String ids) {
        List<Long> longList = Convert.toLongList(ids);
        return terminalFaultService.removeByIds(longList);
    }
}
