package com.centerm.cpay.coms.controller.terminal;

import com.centerm.cpay.terminal.domain.TerminalFlowMonth;
import com.centerm.cpay.terminal.service.ITerminalFlowMonthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.miser.common.annotation.Log;
import org.miser.common.core.page.TableDataInfo;
import org.miser.common.core.support.Convert;
import org.miser.common.enums.BusinessType;
import org.miser.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.miser.common.core.controller.BaseController;

import java.util.List;

/**
 * 终端流量（月统计，只有历史月数据）Controller
 * 
 * <AUTHOR>
 * @date 2019-12-04
 */
@Api(tags = "终端流量（月统计）模块")
@Controller
@RequestMapping("/terminal/flow")
public class TerminalFlowMonthController extends BaseController {

    private String prefix = "terminal/flow";

    @Autowired
    private ITerminalFlowMonthService terminalFlowMonthService;

    @RequiresPermissions("terminal:flow:view")
    @GetMapping()
    public String flow()
    {
        return prefix + "/flow";
    }

    @ApiOperation(value = " 查询终端流量（月统计）列表")
    @RequiresPermissions("terminal:flow:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo<TerminalFlowMonth> list(TerminalFlowMonth terminalFlowMonth) {
        startPage();
        List<TerminalFlowMonth> list = terminalFlowMonthService.selectTerminalFlowMonthList(terminalFlowMonth);
        return getDataTable(list);
    }

    @ApiOperation(value = "导出终端流量（月统计）列表")
    @RequiresPermissions("terminal:flow:export")
    @PostMapping("/export")
    @ResponseBody
    public String export(TerminalFlowMonth terminalFlowMonth) {
        List<TerminalFlowMonth> list = terminalFlowMonthService.selectTerminalFlowMonthList(terminalFlowMonth);
        ExcelUtil<TerminalFlowMonth> util = new ExcelUtil<TerminalFlowMonth>(TerminalFlowMonth.class);
        return util.exportExcel(list, "flow");
    }

    @ApiOperation(value = "新增终端流量（月统计）")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    @ApiOperation(value = "新增保存终端流量（月统计）")
    @RequiresPermissions("terminal:flow:add")
    @Log(title = "终端流量（月统计，只有历史月数据）", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public Boolean addSave(TerminalFlowMonth terminalFlowMonth) {
        return rowResult(terminalFlowMonthService.save(terminalFlowMonth));
    }

    @ApiOperation(value = "修改终端流量（月统计）")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        TerminalFlowMonth terminalFlowMonth = terminalFlowMonthService.getById(id);
        mmap.put("terminalFlowMonth", terminalFlowMonth);
        return prefix + "/edit";
    }

    @ApiOperation(value = "修改保存终端流量（月统计）")
    @RequiresPermissions("terminal:flow:edit")
    @Log(title = "终端流量（月统计，只有历史月数据）", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public Boolean editSave(TerminalFlowMonth terminalFlowMonth) {
        return rowResult(terminalFlowMonthService.updateById(terminalFlowMonth));
    }

    @ApiOperation(value = "删除终端流量（月统计）")
    @RequiresPermissions("terminal:flow:remove")
    @Log(title = "终端流量（月统计，只有历史月数据）", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public Boolean remove(String ids) {
        List<Long> longList = Convert.toLongList(ids);
        return rowResult(terminalFlowMonthService.removeByIds(longList));
    }
}
