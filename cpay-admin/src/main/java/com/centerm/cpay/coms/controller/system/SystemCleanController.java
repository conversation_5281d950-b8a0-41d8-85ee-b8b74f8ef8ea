package com.centerm.cpay.coms.controller.system;

import com.centerm.cpay.common.dto.terminal.SystemCleanInput;
import com.centerm.cpay.task.dto.TusnImport;
import com.centerm.cpay.terminal.service.ISystemCleanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.miser.common.annotation.Log;
import org.miser.common.core.controller.BaseController;
import org.miser.common.core.domain.ExcelImportResult;
import org.miser.common.core.domain.protocol.ResponseMsg;
import org.miser.common.enums.BusinessType;
import org.miser.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 系统清理Controller
 * 
 * <AUTHOR>
 * @date 2020-03-09
 */
@Api(tags = "系统清理模块")
@Controller
@RequestMapping("/system/clean")
public class SystemCleanController extends BaseController {
    private String prefix = "system/clean";

    @Autowired
    private ISystemCleanService systemCleanService;

    @RequiresPermissions("system:clean:view")
    @GetMapping()
    public String clean() {
        return prefix + "/cleanTerminal";
    }

    @ApiOperation(value = "删除终端信息")
    @RequiresPermissions("system:clean:edit")
    @Log(title = "DeleteInsOrTerm", businessType = BusinessType.DELETE)
    @PostMapping( "/deleteInsOrTerm")
    @ResponseBody
    public ResponseMsg<Boolean> remove(SystemCleanInput systemClean) {
        boolean isSuccess = systemCleanService.deleteInsOrTerm(systemClean);
        return ResponseMsg.success(isSuccess);
    }

    @Log(title = "DeleteInsOrTerm ImportData", businessType = BusinessType.IMPORT)
    @RequiresPermissions("system:clean:edit")
    @PostMapping("/importData")
    @ResponseBody
    public ExcelImportResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<TusnImport> util = new ExcelUtil<TusnImport>(TusnImport.class);
        ExcelImportResult<TusnImport> excelImportResult = util.importExcel(file.getInputStream());
        return excelImportResult;
    }
}