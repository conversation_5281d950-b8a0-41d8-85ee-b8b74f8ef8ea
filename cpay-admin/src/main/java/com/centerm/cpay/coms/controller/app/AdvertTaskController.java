package com.centerm.cpay.coms.controller.app;

import com.centerm.cpay.common.domain.task.AdvertTask;
import com.centerm.cpay.task.service.IAdvertTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.miser.common.core.controller.BaseController;
import org.miser.common.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;


@Api(tags = "查询广告信息任务列表")
@RequestMapping("/advert/task")
@Controller
public class AdvertTaskController extends BaseController {

    private String prefix = "advert/info/task";


    @Autowired
    private IAdvertTaskService advertTaskService;


    @RequiresPermissions("advert:info:view")
    @GetMapping()
    public String info() {
        return prefix + "/info";
    }

    @ApiOperation(value = "查询广告信息任务列表")
    @RequiresPermissions("advert:info:list")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo<AdvertTask> list(AdvertTask advertTask) {
        startPage();
        List<AdvertTask> list = advertTaskService.selectTaskList(advertTask);
        return getDataTable(list);
    }

}