package com.centerm.cpay.coms.controller.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.centerm.cpay.common.domain.task.OperationTask;
import com.centerm.cpay.common.dto.task.DownloadTaskDto;
import com.centerm.cpay.common.enums.task.OperationStatusEnum;
import com.centerm.cpay.task.service.IOperationTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.miser.common.annotation.Log;
import org.miser.common.core.controller.BaseController;
import org.miser.common.core.page.TableDataInfo;
import org.miser.common.core.support.Convert;
import org.miser.common.enums.BusinessType;
import org.miser.common.exception.BusinessException;
import org.miser.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 终端操作详细Controller
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
@Api(tags = "终端操作详细管理")
@RequestMapping("/task/operationTask")
@Controller
public class OperationTaskController extends BaseController {

    private String prefix = "task/operation/task";


    @Autowired
    private IOperationTaskService operationTaskService;


//    @RequiresPermissions("task:downloadJob:view")
//    @GetMapping()
//    public String job() {
//        return prefix + "/info";
//    }


    @RequiresPermissions("task:operationJob:view")
    @GetMapping()
    public String task(OperationTask operationTask, ModelMap mmap) {
        mmap.put("jobId", operationTask.getJobId());
        return prefix + "/info";
    }

    @ApiOperation(value = "查询终端操作详细列表")
    @RequiresPermissions("task:operationJob:list")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo<OperationTask> list(OperationTask operationTask) {
        startPage();
        List<OperationTask> list = operationTaskService.selectOperationTaskList(operationTask);
        return getDataTable(list);
    }


    @ApiOperation(value = "导出终端操作详细列表")
    @RequiresPermissions("task:operationTask:export")
    @PostMapping("/export")
    @ResponseBody
    public String export(OperationTask operationTask) {
        List<OperationTask> list = operationTaskService.selectOperationTaskList(operationTask);
        ExcelUtil<OperationTask> util = new ExcelUtil<>(OperationTask.class);
        return util.exportExcel(list, "task");
    }


    @ApiOperation(value = "添加终端操作详细")
    @RequiresPermissions("task:operationTask:add")
    @Log(title = "Remote Instruct", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public Boolean addSave(@RequestBody OperationTask operationTask) {
        return rowResult(operationTaskService.insertOperationTask(operationTask));
    }


    @ApiOperation(value = "修改保存终端操作详细")
    @RequiresPermissions("task:operationTask:edit")
    @Log(title = "Remote Instruct", businessType = BusinessType.UPDATE)
    @PatchMapping("/edit")
    @ResponseBody
    public Boolean editSave(@RequestBody OperationTask operationTask) {
        return rowResult(operationTaskService.updateOperationTask(operationTask));
    }


    @ApiOperation(value = "删除终端操作详细")
    @RequiresPermissions("task:operationTask:remove")
    @Log(title = "Remote Instruct", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public Boolean remove(String ids) {
        return rowResult(operationTaskService.removeByIds(Convert.toLongList(ids)));
    }

    @ApiOperation(value = "重置任务")
    @RequiresPermissions("task:operationTask:reset")
    @Log(title = "Remote Instruct Reset", businessType = BusinessType.UPDATE)
    @PostMapping("/reset")
    @ResponseBody
    public Boolean reset(String ids) {
        List<Long> idList = Convert.toLongList(ids);
        OperationTask uploadTask = new OperationTask();
        uploadTask.setStatus(OperationStatusEnum.TO_RELEASE.ordinal());
        List<OperationTask> taskList = operationTaskService.list(new QueryWrapper<OperationTask>().lambda()
                .in(OperationTask::getId, idList));
        for (OperationTask operationTask : taskList){
            if (operationTask.getStatus() == 4){
                throw new BusinessException("task.status.operation.not.allowed");
            }
        }
        operationTaskService.resetToCmd(taskList);
        return rowResult(operationTaskService.update(uploadTask, new QueryWrapper<OperationTask>().lambda()
                .in(OperationTask::getId, idList)
        ));
    }
}