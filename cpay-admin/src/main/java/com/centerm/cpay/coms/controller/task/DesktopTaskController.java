package com.centerm.cpay.coms.controller.task;


import com.centerm.cpay.common.domain.task.DesktopTask;
import com.centerm.cpay.task.service.IDesktopTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.miser.common.annotation.Log;
import org.miser.common.core.controller.BaseController;
import org.miser.common.core.page.TableDataInfo;
import org.miser.common.core.support.Convert;
import org.miser.common.enums.BusinessType;
import org.miser.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 桌面定制任务Controller
 *
 * <AUTHOR>
 * @date 2019-12-17
 */
@Api(tags = "桌面定制任务模块")
@Controller
@RequestMapping("/desktop/task")
public class DesktopTaskController extends BaseController {
    private String prefix = "task/desktop/task";

    @Autowired
    private IDesktopTaskService desktopTaskService;

    @RequiresPermissions("desktop:task:view")
    @GetMapping(value = {"","/{id}"})
    public String task(@PathVariable(value="id",required=false)Long id, ModelMap mmap) {
        mmap.put("jobId", id);
        return prefix + "/info";
    }

    @ApiOperation(value = " 查询桌面定制任务列表")
    @RequiresPermissions("desktop:task:list")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo<DesktopTask> list(DesktopTask desktopTask) {
        startPage();
        List<DesktopTask> list = desktopTaskService.selectDesktopTaskList(desktopTask);
        return getDataTable(list);
    }


    @ApiOperation(value = "导出桌面定制任务列表")
    @RequiresPermissions("desktop:task:export")
    @PostMapping("/export")
    @ResponseBody
    public String export(DesktopTask desktopTask) {
        List<DesktopTask> list = desktopTaskService.selectDesktopTaskList(desktopTask);
        ExcelUtil<DesktopTask> util = new ExcelUtil<DesktopTask>(DesktopTask.class);
        return util.exportExcel(list, "task");
    }


    @ApiOperation(value = "新增桌面定制任务页面")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }


    @ApiOperation(value = "新增保存桌面定制任务")
    @RequiresPermissions("desktop:task:add")
    @Log(title = "桌面定制任务", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public Boolean addSave(DesktopTask desktopTask) {
        return rowResult(desktopTaskService.save(desktopTask));
    }


    @ApiOperation(value = "修改桌面定制任务页面")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        DesktopTask desktopTask = desktopTaskService.selectDesktopTaskById(id);
        mmap.put("desktopTask", desktopTask);
        return prefix + "/edit";
    }


    @ApiOperation(value = "修改保存桌面定制任务")
    @RequiresPermissions("desktop:task:edit")
    @Log(title = "桌面定制任务", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public Boolean editSave(DesktopTask desktopTask) {
        return rowResult(desktopTaskService.updateById(desktopTask));
    }


    @ApiOperation(value = "删除桌面定制任务")
    @RequiresPermissions("desktop:task:remove")
    @Log(title = "桌面定制任务", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public Boolean remove(@RequestBody String ids) {
        List<Long> idsList = Convert.toLongList(ids);
        return desktopTaskService.removeByIds(idsList);
    }
}
