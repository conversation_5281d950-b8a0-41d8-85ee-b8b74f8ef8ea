package com.centerm.cpay.coms.controller.app;

import com.centerm.cpay.common.domain.app.AppInfo;
import com.centerm.cpay.common.domain.app.AppType;
import com.centerm.cpay.common.domain.terminal.TerminalGroup;
import com.centerm.cpay.common.dto.app.AppAuditInput;
import com.centerm.cpay.common.enums.app.AppStatusEnum;
import com.centerm.cpay.app.domain.AppLabel;
import com.centerm.cpay.app.service.IAppInfoService;
import com.centerm.cpay.app.service.IAppLabelService;
import com.centerm.cpay.app.service.IAppTypeService;
import com.centerm.cpay.terminal.service.ITerminalFirmService;
import com.centerm.cpay.terminal.service.ITerminalGroupService;
import com.centerm.cpay.terminal.service.ITerminalTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.miser.common.annotation.Log;
import org.miser.common.constant.Constants;
import org.miser.common.core.controller.BaseController;
import org.miser.common.core.page.TableDataInfo;
import org.miser.common.core.support.Convert;
import org.miser.common.enums.BusinessType;
import org.miser.common.utils.data.ApiAssert;
import org.miser.common.utils.data.CtUtils;
import org.miser.framework.util.ShiroUtils;
import org.miser.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 审核应用
 *
 * <AUTHOR>
 * @date 2019/09/27 17:19
 **/
@Api(tags = "审核应用")
@RequestMapping("/app/audit")
@Controller
public class AppAuditController extends BaseController {

    private String prefix = "app/audit";

    @Autowired
    private IAppInfoService appInfoService;

    @Autowired
    private ITerminalGroupService terminalGroupService;

    @Autowired
    private IAppTypeService appTypeService;

    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private IAppLabelService appLabelService;
    @Autowired
    private ITerminalFirmService terminalFirmService;
    @Autowired
    private ITerminalTypeService terminalTypeService;

    @RequiresPermissions("app:audit:auditList")
    @GetMapping()
    public String info(ModelMap mmap) {
        Long deptId = ShiroUtils.getDeptId();
        TerminalGroup terminalGroup = new TerminalGroup();
        terminalGroup.setDeptId(deptId);
        AppType appType = new AppType();
        appType.setDeptId(deptId);
        AppLabel appLabel = new AppLabel();
        appLabel.setDeptId(deptId);
        mmap.put("terminalGroupList", terminalGroupService.selectTerminalGroupListByCondition(terminalGroup));
        mmap.put("appTypeList", appTypeService.selectAppTypeList(appType));
        mmap.put("appLabelList", appLabelService.selectAppLabelList(appLabel, deptId));
        return prefix + "/auditList";
    }

    @ApiOperation(value = "审核应用信息页面")
    @RequiresPermissions("app:audit:apk")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        validId(id);
        AppInfo appInfo = appInfoService.getAppInfoById(id);
        mmap.put("appInfo", appInfo);
        mmap.put("firm", terminalFirmService.getById(appInfo.getFirmId()));
        String terminalTypes = appInfo.getTerminalTypes();
        if (!CtUtils.isEmpty(terminalTypes)) {
            List<Long> termTypeId = Convert.toLongList(terminalTypes.substring(1, terminalTypes.length() - 1));
            mmap.put("terminalTypeList", terminalTypeService.selectTerminalTypeByIds(termTypeId));
        } else {
            mmap.put("terminalTypeList", null);
        }
        return prefix + "/audit";
    }

    @ApiOperation(value = "查询审核应用列表")
    @RequiresPermissions("app:audit:auditList")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo<AppInfo> getAuditApkList(AppInfo appInfo) {
        startPage();
        appInfo.setIsDeleted(Constants.NO);
        appInfo.setStatus(AppStatusEnum.TO_BE_AUDITED.ordinal());
        //只查询运维数据
        //appInfo.setApkCopsSign(PlatformType.COMS.getValue());
        List<AppInfo> list = appInfoService.getApkList(appInfo);
        return getDataTable(list);
    }

    @ApiOperation(value = "提交审核")
    @RequiresPermissions("app:audit:submit")
    @PostMapping("/submit")
    @Transactional(rollbackFor = Exception.class)
    @ResponseBody
    public boolean submitAudit(Long id) {
        AppInfo queryAppInfo = appInfoService.getById(id);
        ApiAssert.isTrue("app.no.update", AppStatusEnum.ON_SHELVES.ordinal() != queryAppInfo.getStatus());
        AppInfo appInfo = new AppInfo();
        appInfo.setUpdateBy(ShiroUtils.getLoginName());
        appInfo.setStatus(AppStatusEnum.TO_BE_AUDITED.ordinal());
        appInfo.setId(id);
        return rowResult(appInfoService.updateById(appInfo));
    }

    @ApiOperation(value = "审核应用")
    @RequiresPermissions("app:audit:apk")
    @Log(title = "AppAudit", businessType = BusinessType.AUDITED)
    @PostMapping("/apk")
    @ResponseBody
    public Boolean audit(@Validated AppAuditInput appAuditInput) {
        appAuditInput.setReviewUser(ShiroUtils.getLoginName());
        return appInfoService.auditAppInfo(appAuditInput);
    }
}
