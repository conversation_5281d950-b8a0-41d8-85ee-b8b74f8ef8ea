package com.centerm.cpay.coms.controller.system;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.miser.common.annotation.Log;
import org.miser.common.enums.BusinessType;
import org.miser.common.utils.data.ApiAssert;
import org.miser.common.utils.data.DateUtils;
import org.miser.common.utils.poi.ExcelUtil;
import org.miser.file.fastdfs.FastDFSClient;
import org.miser.file.service.IFileService;
import org.miser.file.util.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@Api(tags = "【文件请求】上传/下载/浏览")
@Slf4j
@Controller
public class FileController {

    @Autowired
    private IFileService fileService;

    @ApiOperation(value = "临时文件(导出)下载", notes = "导出数据下载地址")
    @ApiImplicitParams({@ApiImplicitParam(name = "fileName", value = "文件名", required = true, dataType = "string", paramType = "query")})
    @GetMapping("tmp/download")
    public void tmpDownload(@RequestParam String fileName, HttpServletResponse response, HttpServletRequest request) throws IOException {
        String realFileName = DateUtils.dateTimeNow() + fileName.substring(fileName.indexOf("_") + 1);
        String filePath = ExcelUtil.getAbsoluteFile(fileName);
        downloadFile(response, request, filePath, realFileName);
        FileUtils.deleteFile(filePath);
    }

    @ApiOperation(value = "临时文件下载/浏览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileName", value = "文件路径", required = true, dataType = "string", paramType = "fileName")})
    @GetMapping("/file/tmp/view")
    public void fileTmpDownload(@RequestParam String fileName, HttpServletResponse response, HttpServletRequest request) throws IOException {
        if (fileName.startsWith("group")) {
            new FastDFSClient().downloadFile(fileName, response);
            return;
        }
        String filePath = fileService.getTempFile() + fileName;
        log.info("下载文件地址【{}】", filePath);
        downloadFile(response, request, filePath, fileName);
    }

    @ApiOperation(value = "文件下载/浏览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileName", value = "文件路径", required = true, dataType = "string", paramType = "fileName")})
    @GetMapping("/file/view")
    public void fileDownload(@RequestParam String fileName, HttpServletResponse response, HttpServletRequest request) throws IOException {
        if (fileName.startsWith("group")) {
            new FastDFSClient().downloadFile(fileName, response);
            return;
        }
        String filePath = fileService.getSaveFile() + fileName;
        log.info("下载文件地址【{}】", filePath);
        downloadFile(response, request, filePath, fileName);
    }

    private void downloadFile(HttpServletResponse response, HttpServletRequest request, String filePath, String fileName) throws IOException {
        ApiAssert.isTrue("file.name.error", FileUtils.isValidFilename(fileName));
        String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
        response.setCharacterEncoding("utf-8");
        response.setContentType("multipart/form-data");
        response.setHeader("Content-Disposition",
                "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, realFileName));
        FileUtils.writeBytes(filePath, response.getOutputStream());
    }

    @ApiOperation(value = "文件上传", notes = "上传文件到临时目录,限制图片")
    @ApiImplicitParams({@ApiImplicitParam(name = "file", value = "上传文件到临时目录", required = true, dataType = "__File")})
    @Log(title = "Upload Tmp Direct", businessType = BusinessType.UPDATE)
    @PostMapping("/file/upload/tmp")
    @ResponseBody
    public String fileUpload(@RequestParam MultipartFile file) {
        return fileService.uploadTmpFile(file);
    }



//    @Log(title = "File delete", businessType = BusinessType.UPDATE)
    @PostMapping("/file/delete")
    @ResponseBody
    public void fileUpload(@RequestParam String key, @RequestParam String id) {


    }

}