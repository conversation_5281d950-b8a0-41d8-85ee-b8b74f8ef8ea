package com.centerm.cpay.coms.controller.task;

import com.centerm.cpay.common.domain.task.ChangeInsTask;
import com.centerm.cpay.task.service.IChangeInsTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.miser.common.core.controller.BaseController;
import org.miser.common.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;


@Api(tags = "终端机构变更管理")
@RequestMapping("/task/changInsTask")
@Controller
public class ChangeInsTaskController extends BaseController {

    private String prefix = "task/changins/task";


    @Autowired
    private IChangeInsTaskService iChangeInsTaskService;


    @RequiresPermissions("task:changInsJob:view")
    @GetMapping()
    public String info() {
        return prefix + "/info";
    }

    @ApiOperation(value = "查询终端机构变更任务列表")
    @RequiresPermissions("task:changInsJob:list")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo<ChangeInsTask> list(ChangeInsTask changeInsTask) {
        startPage();
        List<ChangeInsTask> list = iChangeInsTaskService.selectTaskList(changeInsTask);
        return getDataTable(list);
    }

}