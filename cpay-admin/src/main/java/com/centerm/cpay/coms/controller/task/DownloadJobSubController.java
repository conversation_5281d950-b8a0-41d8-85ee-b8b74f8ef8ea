package com.centerm.cpay.coms.controller.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.miser.common.annotation.Log;
import org.miser.common.core.domain.IdsInput;
import org.miser.common.core.page.TableDataInfo;
import org.miser.common.enums.BusinessType;
import org.miser.common.core.support.Convert;
import org.miser.common.utils.poi.ExcelUtil;
import org.miser.common.core.controller.BaseController;
import com.centerm.cpay.task.domain.DownloadJobSub;
import com.centerm.cpay.task.service.IDownloadJobSubService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 子任务详情Controller
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
@Api(tags = "子任务详情管理")
@RequestMapping("/task/downloadJobSub")
@RestController
public class DownloadJobSubController extends BaseController {


    @Autowired
    private IDownloadJobSubService downloadJobSubService;


    @ApiOperation(value = "查询子任务详情列表")
    @RequiresPermissions("task:downloadJobSub:list")
    @GetMapping("/list")
    public TableDataInfo<DownloadJobSub> list(DownloadJobSub downloadJobSub) {
        startPage();
        List<DownloadJobSub> list = downloadJobSubService.selectDownloadJobSubList(downloadJobSub);
        return getDataTable(list);
    }


    @ApiOperation(value = "导出子任务详情列表")
    @RequiresPermissions("task:downloadJobSub:export")
    @PostMapping("/export")
    public String export(DownloadJobSub downloadJobSub) {
        List<DownloadJobSub> list = downloadJobSubService.selectDownloadJobSubList(downloadJobSub);
        ExcelUtil<DownloadJobSub> util = new ExcelUtil<>(DownloadJobSub.class);
        return util.exportExcel(list, "jobSub");
    }


    @ApiOperation(value = "添加子任务详情")
    @RequiresPermissions("task:downloadJobSub:add")
    @Log(title = "Subtask Details", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public Boolean addSave(@RequestBody DownloadJobSub downloadJobSub) {
        return rowResult(downloadJobSubService.insertDownloadJobSub(downloadJobSub));
    }


    @ApiOperation(value = "修改保存子任务详情")
    @RequiresPermissions("task:downloadJobSub:edit")
    @Log(title = "Subtask Details", businessType = BusinessType.UPDATE)
    @PatchMapping("/edit")
    public Boolean editSave(@RequestBody DownloadJobSub downloadJobSub) {
        return rowResult(downloadJobSubService.updateDownloadJobSub(downloadJobSub));
    }


    @ApiOperation(value = "删除子任务详情")
    @RequiresPermissions("task:downloadJobSub:remove")
    @Log(title = "Subtask Details", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public Boolean remove(String ids) {

        return rowResult(downloadJobSubService.removeByIds(Convert.toLongList(ids)));
    }
}