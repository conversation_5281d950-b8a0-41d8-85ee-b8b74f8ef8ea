package com.centerm.cpay.coms.controller.app;

import com.centerm.cpay.app.domain.DeptLabel;
import com.centerm.cpay.app.service.IAppLabelService;
import com.centerm.cpay.app.service.IDeptLabelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.miser.common.annotation.Log;
import org.miser.common.core.controller.BaseController;
import org.miser.common.core.page.TableDataInfo;
import org.miser.common.core.support.Convert;
import org.miser.common.enums.BusinessType;
import org.miser.common.utils.data.ApiAssert;
import org.miser.common.utils.data.CtUtils;
import org.miser.common.utils.poi.ExcelUtil;
import org.miser.framework.util.ShiroUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 机构标签关联Controller
 *
 * <AUTHOR>
 * @date 2020-03-03
 */
@Api(tags = "机构标签关联模块")
@Controller
@RequestMapping("/app/deptLabel")
public class DeptLabelController extends BaseController {
    private String prefix = "app/deptLabel";

    @Autowired
    private IDeptLabelService deptLabelService;
    @Autowired
    private IAppLabelService appLabelService;

    @RequiresPermissions("app:deptLabel:view")
    @GetMapping()
    public String label(ModelMap mmap) {
        mmap.put("deptId", ShiroUtils.getDeptId());
        return prefix + "/deptLabel";
    }


    @ApiOperation(value = " 查询机构标签关联列表")
    @RequiresPermissions("app:deptLabel:list")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo<DeptLabel> list(DeptLabel deptLabel) {
        startPage();
        if (CtUtils.isEmpty(deptLabel.getDeptId())) {
            deptLabel.setDeptId(ShiroUtils.getDeptId());
        }
        List<DeptLabel> list = deptLabelService.selectDeptLabelList(deptLabel);
        return getDataTable(list);
    }

    @ApiOperation(value = "导出机构标签关联列表")
    @RequiresPermissions("app:deptLabel:export")
    @PostMapping("/export")
    @ResponseBody
    public String export(DeptLabel deptLabel) {
        if (CtUtils.isEmpty(deptLabel.getDeptId())) {
            deptLabel.setDeptId(ShiroUtils.getDeptId());
        }
        List<DeptLabel> list = deptLabelService.selectDeptLabelList(deptLabel);
        ExcelUtil<DeptLabel> util = new ExcelUtil<DeptLabel>(DeptLabel.class);
        return util.exportExcel(list, "label");
    }


    @ApiOperation(value = "新增机构标签关联页面")
    @GetMapping("/add/{deptId}")
    public String add(@PathVariable("deptId") Long deptId, ModelMap mmap) {
        if (CtUtils.isEmpty(deptId)) {
            deptId = ShiroUtils.getDeptId();
        }
        mmap.put("deptId", deptId);
        mmap.put("appLabelList", appLabelService.selectDeptNotLabelList(null, deptId));
        return prefix + "/add";
    }


    @ApiOperation(value = "新增保存机构标签关联")
    @RequiresPermissions("app:deptLabel:add")
    @Log(title = "机构标签关联", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public Boolean addSave(String labelIds, Long deptId) {
        String loginName = ShiroUtils.getLoginName();
        List<Long> labelIdsList = Convert.toLongList(labelIds);

        List<DeptLabel> deptLabelList = labelIdsList.stream().map(
                labelId -> {
                    DeptLabel deptLabel = new DeptLabel();
                    deptLabel.setDeptId(deptId);
                    deptLabel.setLabelId(labelId);
                    deptLabel.setCreateBy(loginName);
                    return deptLabel;
                }
        ).collect(Collectors.toList());
        return rowResult(deptLabelService.saveBatch(deptLabelList));
    }

    @ApiOperation(value = "删除机构标签关联")
    @RequiresPermissions("app:deptLabel:remove")
    @Log(title = "机构标签关联", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public Boolean remove(@NotNull String labelIds, @NotNull Long deptId) {
        List<Long> labelIdsList = Convert.toLongList(labelIds);
        if (CtUtils.isEmpty(labelIdsList)) {
            ApiAssert.failure("param.not.null", "labelIds");
        }
        return deptLabelService.removeByDeptLabelIds(labelIdsList, deptId);
    }
}
