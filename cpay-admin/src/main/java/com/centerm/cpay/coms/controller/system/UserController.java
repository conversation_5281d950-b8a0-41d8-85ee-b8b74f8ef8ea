package com.centerm.cpay.coms.controller.system;


import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.miser.common.annotation.Log;
import org.miser.common.constant.UserConstants;
import org.miser.common.core.controller.BaseController;
import org.miser.common.core.domain.ResponseResult;
import org.miser.common.core.page.TableDataInfo;
import org.miser.common.enums.BusinessType;
import org.miser.common.exception.BusinessException;
import org.miser.common.utils.data.ApiAssert;
import org.miser.common.utils.data.CommonUtils;
import org.miser.common.utils.data.CtUtils;
import org.miser.common.utils.data.StringUtils;
import org.miser.common.utils.poi.ExcelUtil;
import org.miser.framework.shiro.service.PasswordService;
import org.miser.framework.util.ShiroUtils;
import org.miser.system.domain.SysRole;
import org.miser.system.domain.SysUser;
import org.miser.system.service.ISysPostService;
import org.miser.system.service.ISysRoleService;
import org.miser.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/system/user")
@Slf4j
public class UserController extends BaseController {
    private String prefix = "system/user";

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysPostService postService;

    @Autowired
    private PasswordService passwordService;

    @RequiresPermissions("system:user:view")
    @GetMapping()
    public String user() {
        return prefix + "/user";
    }

    @RequiresPermissions("system:user:list")
    @GetMapping("/list")
    @ResponseBody
    public TableDataInfo<SysUser> list(SysUser user) {
        startPage();
        if(CtUtils.isEmpty(user.getDeptId())){
            user.setDeptId(ShiroUtils.getDeptId());
        }
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    @Log(title = "SysUser", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:user:export")
    @PostMapping("/export")
    @ResponseBody
    public String export(SysUser user) {
        List<SysUser> list = userService.selectUserList(user);
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        return util.exportExcel(list, "user");
    }

    @Log(title = "SysUser", businessType = BusinessType.IMPORT)
    @RequiresPermissions("system:user:import")
    @PostMapping("/importData")
    @ResponseBody
    public String importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        List<SysUser> userList = util.importExcel(file.getInputStream()).getSuccessList();
        String operName = ShiroUtils.getSysUser().getLoginName();
        String message = userService.importUser(userList, updateSupport, operName);
        return message;
    }

    @RequiresPermissions("system:user:view")
    @GetMapping("/importTemplate")
    @ResponseBody
    public String importTemplate() {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        return util.importTemplateExcel("ImportUser");
    }

    /**
     * 新增用户
     */
    @GetMapping("/add")
    public String add(ModelMap mmap) {
        List<SysRole> roleList = roleService.selectRoleAll();
        if (SysUser.isAdmin(ShiroUtils.getUserId())){
            mmap.put("roles",roleList);
        }else {
            roleList.removeIf(role -> role.getRoleId()== 1 || role.getRoleId() == 2);
//            roleList.remove(0);
            mmap.put("roles",roleList);
        }
        mmap.put("posts", postService.selectPostAll());
        return prefix + "/add";
    }

    /**
     * 添加用户
     */
    @RequiresPermissions("system:user:add")
    @Log(title = "SysUser", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    @ResponseBody
    public Integer addSave(SysUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && SysUser.isAdmin(user.getUserId())) {
            throw new BusinessException("system.admin.not.update");
        }
        user.setUserName(user.getLoginName());
        user.setSalt(ShiroUtils.randomSalt());
        user.setPassword(passwordService.encryptPassword(user.getLoginName(),user.getPassword(), user.getSalt()));
        user.setCreateBy(ShiroUtils.getLoginName());
        return userService.insertUser(user);
    }

    /**
     * 修改用户
     */
    @GetMapping("/edit/{userId}")
    public String edit(@PathVariable Long userId, ModelMap mmap) {
        mmap.put("user", userService.selectUserById(userId));
        List<SysRole> roleList = roleService.selectRolesByUserId(userId);
        if (SysUser.isAdmin(ShiroUtils.getUserId())){
            mmap.put("roles",roleList);
        }else {
            roleList.removeIf(role -> role.getRoleId()== 1 || role.getRoleId() == 2);
            mmap.put("roles",roleList);
        }
        mmap.put("posts", postService.selectPostsByUserId(userId));
        return prefix + "/edit";
    }

    /**
     * 修改保存用户
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "SysUser", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @Transactional(rollbackFor = Exception.class)
    @ResponseBody
    public Integer editSave(SysUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && SysUser.isAdmin(user.getUserId())) {
            throw new BusinessException("system.admin.not.update");
        }
        user.setUpdateBy(ShiroUtils.getLoginName());
        return userService.updateUser(user);
    }

    @RequiresPermissions("system:user:resetPwd")
    @Log(title = "SysUser", businessType = BusinessType.UPDATE)
    @GetMapping("/resetPwd/{userId}")
    public String resetPwd(@PathVariable("userId") Long userId, ModelMap mmap) {
        mmap.put("user", userService.selectUserById(userId));
        return prefix + "/resetPwd";
    }

    @RequiresPermissions("system:user:resetPwd")
    @Log(title = "SysUser", businessType = BusinessType.UPDATE)
    @PostMapping("/resetPwd")
    @ResponseBody
    public Integer resetPwdSave(SysUser user) {
        user.setSalt(ShiroUtils.randomSalt());
        user.setPassword(passwordService.encryptPassword(user.getLoginName(), user.getPassword(), user.getSalt()));
        return userService.resetUserPwd(user);
    }

    @RequiresPermissions("system:user:remove")
    @Log(title = "SysUser", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public Integer remove(String ids) {
        return userService.deleteUserByIds(ids);
    }

    /**
     * 校验用户名
     */
    @PostMapping("/checkLoginNameUnique")
    @ResponseBody
    public String checkLoginNameUnique(SysUser user) {
        return userService.checkLoginNameUnique(user.getLoginName());
    }

    /**
     * 校验手机号码
     */
    @PostMapping("/checkPhoneUnique")
    @ResponseBody
    public String checkPhoneUnique(SysUser user) {
        return userService.checkPhoneUnique(user);
    }

    /**
     * 校验email邮箱
     */
    @PostMapping("/checkEmailUnique")
    @ResponseBody
    public String checkEmailUnique(SysUser user) {
        return userService.checkEmailUnique(user);
    }

    /**
     * 用户状态修改
     */
    @Log(title = "SysUser", businessType = BusinessType.UPDATE)
    @RequiresPermissions("system:user:edit")
    @PostMapping("/changeStatus")
    @ResponseBody
    public Integer changeStatus(SysUser user) {
        return userService.changeStatus(user);
    }
    /**
     * 用户模拟登录
     */
    @Log(title = "SysUser", businessType = BusinessType.OTHER)
    @GetMapping("/changeLogin/{loginName}")
    @RequiresPermissions("user:auth")
    @ResponseBody
    public ResponseResult changeLogin(@PathVariable String loginName) {
        if(StringUtils.isEmpty(loginName)){
            throw new BusinessException("login.name.null");
        }
        if(!"admin".equals(ShiroUtils.getLoginName())){
            throw new BusinessException("user.not.admin");
        }
        SysUser user = userService.selectUserByLoginName(loginName);
        ShiroUtils.setSysUser(user);
        return success();
    }
}