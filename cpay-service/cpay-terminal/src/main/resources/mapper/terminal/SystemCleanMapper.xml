<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.cpay.terminal.mapper.SystemCleanMapper">

    <delete id="cleanTerm" parameterType="java.lang.String" statementType="CALLABLE">
    	<![CDATA[
    	{call cleanSystemTerminal (#{tusn,mode=IN,jdbcType=VARCHAR})}
		]]>
  	</delete>
    <delete id="cleanDept" parameterType="java.lang.Long">
		delete from coms_advert_info where dept_id = #{deptId};
		delete from coms_app_info where dept_id = #{deptId};
		delete from coms_app_label where dept_id = #{deptId};
		delete from coms_dept_label where dept_id = #{deptId};
		delete from coms_desktop_info where dept_id = #{deptId};
		delete from coms_desktop_job where dept_id = #{deptId};
		delete from coms_download_job where dept_id = #{deptId};
		delete from coms_move_strategy_scope where dept_id = #{deptId};
		delete from coms_operation_job where dept_id = #{deptId};
		delete from coms_soft_info where dept_id = #{deptId};
		delete from coms_task_job where dept_id = #{deptId};
		delete from coms_terminal_group where dept_id = #{deptId};
		delete from coms_terminal_info where dept_id = #{deptId};
		delete from coms_terminal_param where dept_id = #{deptId};
		delete from coms_upload_job where dept_id = #{deptId};
  	</delete>
	<delete id="cleanNoJobTask">
		DELETE from coms_desktop_task where id  in (select s.id from (select t.id from  coms_desktop_task t left join coms_desktop_job j on t.job_id =j.id where j.id is Null) s);
		DELETE from coms_download_job_app where id in  (select t.id from(select a.id from coms_download_job_app a LEFT JOIN coms_download_job j ON  a.job_id=j.id where j.id is NULL) t);
		DELETE from coms_download_job_sub where id in  (select t.id from(select a.id from coms_download_job_sub a LEFT JOIN coms_download_job j ON  a.job_id=j.id where j.id is NULL) t);
		DELETE from coms_download_task where id in  (select t.id from(select a.id from coms_download_task a LEFT JOIN coms_download_job j ON  a.job_id=j.id where j.id is NULL) t);
		DELETE from coms_operation_task where id in  (select t.id from(select a.id from coms_operation_task a LEFT JOIN coms_operation_job j ON  a.job_id=j.id where j.id is NULL) t);
		DELETE from coms_task_job_sub where id in  (select t.id from(select a.id from coms_task_job_sub a LEFT JOIN coms_task_job j ON  a.job_id=j.id where j.id is NULL) t);
		DELETE from coms_task_record where id in  (select t.id from(select a.id from coms_task_record a LEFT JOIN coms_task_job j ON  a.job_id=j.id where j.id is NULL) t);
		DELETE from coms_upload_task where id in  (select t.id from(select a.id from coms_upload_task a LEFT JOIN coms_upload_job j ON  a.job_id=j.id where j.id is NULL) t);
  	</delete>
	<delete id="batchCleanTerminal" parameterType="java.util.List">
		delete from coms_download_limit where tusn IN
		<foreach collection="list" index="index" open="("  separator="," close=")" item="id">
			#{id}
		</foreach>;
		delete from coms_download_task where tusn IN
		<foreach collection="list" index="index" open="("  separator="," close=")" item="id">
			#{id}
		</foreach>;
		delete from coms_move_strategy_scope where tusn IN
		<foreach collection="list" index="index" open="("  separator="," close=")" item="id">
			#{id}
		</foreach>;
		delete from coms_operation_task where tusn IN
		<foreach collection="list" index="index" open="("  separator="," close=")" item="id">
			#{id}
		</foreach>;
		delete from coms_task_record where tusn IN
		<foreach collection="list" index="index" open="("  separator="," close=")" item="id">
			#{id}
		</foreach>;
		delete from coms_terminal_alarm_info where tusn IN
		<foreach collection="list" index="index" open="("  separator="," close=")" item="id">
			#{id}
		</foreach>;
		delete from coms_terminal_app where tusn IN
		<foreach collection="list" index="index" open="("  separator="," close=")" item="id">
			#{id}
		</foreach>;
		delete from coms_terminal_dyn_info where tusn IN
		<foreach collection="list" index="index" open="("  separator="," close=")" item="id">
			#{id}
		</foreach>;
		delete from coms_terminal_flow_day where tusn IN
		<foreach collection="list" index="index" open="("  separator="," close=")" item="id">
			#{id}
		</foreach>;
		delete from coms_terminal_flow_month where tusn IN
		<foreach collection="list" index="index" open="("  separator="," close=")" item="id">
			#{id}
		</foreach>;
		delete from coms_terminal_info where tusn IN
		<foreach collection="list" index="index" open="("  separator="," close=")" item="id">
			#{id}
		</foreach>;
		delete from coms_terminal_move_record where tusn IN
		<foreach collection="list" index="index" open="("  separator="," close=")" item="id">
			#{id}
		</foreach>;
		delete from coms_terminal_position where tusn IN
		<foreach collection="list" index="index" open="("  separator="," close=")" item="id">
			#{id}
		</foreach>;
		delete from coms_terminal_power where tusn IN
		<foreach collection="list" index="index" open="("  separator="," close=")" item="id">
			#{id}
		</foreach>;
		delete from coms_terminal_status where tusn IN
		<foreach collection="list" index="index" open="("  separator="," close=")" item="id">
			#{id}
		</foreach>;
		delete from coms_terminal_sysdetail where tusn IN
		<foreach collection="list" index="index" open="("  separator="," close=")" item="id">
			#{id}
		</foreach>;
		delete from coms_upload_task where tusn IN
		<foreach collection="list" index="index" open="("  separator="," close=")" item="id">
			#{id}
		</foreach>;
	</delete>
	<select id="findDeptChild"  parameterType="Long" resultType="Long">
		select dept_id from sys_dept
		where find_in_set(#{deptId}, ancestors);
	</select>
</mapper>