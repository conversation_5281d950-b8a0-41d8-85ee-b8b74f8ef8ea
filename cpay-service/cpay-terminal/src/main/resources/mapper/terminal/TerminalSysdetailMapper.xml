<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.cpay.terminal.mapper.TerminalSysdetailMapper">

    <resultMap type="TerminalSysdetail" id="TerminalSysdetailResult">
        <result property="id" column="id"/>
        <result property="tusn" column="tusn"/>
        <result property="sysDetail" column="sys_detail"/>
        <result property="commParaVersion" column="comm_para_version"/>
        <result property="launcherParaVersion" column="launcher_para_version"/>
        <result property="osVersion" column="os_version"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTerminalSysdetailVo">
        select a.id, a.tusn, a.sys_detail, a.comm_para_version, a.launcher_para_version, a.os_version, a.create_time, a.update_time from
        coms_terminal_sysdetail a
    </sql>

    <select id="selectTerminalSysdetailList" parameterType="TerminalSysdetail" resultMap="TerminalSysdetailResult">
        <include refid="selectTerminalSysdetailVo"/>
        <where>
            <if test="id !=null">and a.id = #{id}</if>
            <if test="deptId!=null">and a.dept_id = #{deptId}
            </if>
            <if test="tusn !=null and tusn !=''">and a.tusn LIKE concat('%',#{tusn},'%')</if>
            <if test="sysDetail !=null and sysDetail !=''">and a.sys_detail = #{sysDetail}</if>
            <if test="commParaVersion !=null and commParaVersion !=''">and a.comm_para_version = #{commParaVersion}</if>
            <if test="launcherParaVersion !=null and launcherParaVersion !=''">and a.launcher_para_version =
                #{launcherParaVersion}
            </if>
            <if test="osVersion !=null and osVersion !=''">and a.os_version = #{osVersion}</if>
            <if test="createTime !=null">and a.create_time = #{createTime}</if>
            <if test="updateTime !=null">and a.update_time = #{updateTime}</if>
        </where>
        order by a.update_time desc,a.create_time desc
    </select>

    <select id="terminalSysDetailVersionStatisticsList" parameterType="Long" resultType="TerminalSysdetailSta">
        select count(0) as count,JSON_UNQUOTE(JSON_EXTRACT(sys_detail, '$.systemVer')) AS version
        FROM coms_terminal_sysdetail where
        ( dept_id = #{deptId}
        OR dept_id IN ( SELECT dept_id FROM sys_dept WHERE FIND_IN_SET ( #{deptId}, ancestors ) ))
        group by version order by version desc limit 10;
    </select>

    <!--模糊查询终端详情信息-->
    <select id="selectByParam" parameterType="TerminalSysdetail" resultMap="TerminalSysdetailResult">
        <include refid="selectTerminalSysdetailVo"/>
        <where>
            <if test="tusn!=null and tusn!=''">and a.tusn LIKE concat('%',#{tusn},'%')</if>
        </where>
    </select>

    <!--根据终端号查询终端详情-->
    <select id="selectByTusn" parameterType="TerminalSysdetail" resultMap="TerminalSysdetailResult">
        <include refid="selectTerminalSysdetailVo"/>
        <where>
            <if test="tusn!=null and tusn!=''">and a.tusn = #{tusn}</if>
        </where>
    </select>

    <!--根据tusn查询终端详情-->
    <select id="selectTerminalSysdetailByTusn" parameterType="String" resultMap="TerminalSysdetailResult">
        <include refid="selectTerminalSysdetailVo"/>
        <where>
            <if test="tusn!=null and tusn!=''">and a.tusn = #{tusn}</if>
        </where>
    </select>

</mapper>