package com.centerm.cpay.terminal.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import org.miser.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 终端告警信息对象 coms_terminal_alarm_info
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
@ApiModel(value = "${tableComment}")
@Data
@ToString
@TableName("coms_terminal_alarm_info")
@Accessors(chain = true)
public class TerminalAlarmInfo {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @Excel(name = "终端序列号")
    @ApiModelProperty(value = "终端序列号")
    private String tusn;

    @Excel(name = "硬件状态图")
    @ApiModelProperty(value = "硬件状态图")
    private String hardwareStatus;

    @Excel(name = "基础信息")
    @ApiModelProperty(value = "基础信息")
    private String baseInfo;

    @Excel(name = "异常信息")
    @ApiModelProperty(value = "异常信息")
    private String exceptionInfo;

    @Excel(name = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @Excel(name = "更新时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}


