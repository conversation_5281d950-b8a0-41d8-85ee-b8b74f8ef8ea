package com.centerm.cpay.terminal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.cpay.terminal.domain.TerminalFlowMonth;
import com.centerm.cpay.terminal.mapper.TerminalFlowMonthMapper;
import com.centerm.cpay.terminal.service.ITerminalFlowMonthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 终端流量（月统计，只有历史月数据）Service业务层处理
 *
 * <AUTHOR>
 * @date 2019-12-04
 */
@Service
public class TerminalFlowMonthServiceImpl extends ServiceImpl<TerminalFlowMonthMapper, TerminalFlowMonth> implements ITerminalFlowMonthService {

    @Autowired
    private TerminalFlowMonthMapper terminalFlowMonthMapper;

    /**
     * 根据条件查询终端按月流量信息列表
     *
     * @param terminalFlowMonth 终端按月流量信息
     * @return 终端按月流量信息列表
     */
    @Override
    public List<TerminalFlowMonth> selectTerminalFlowMonthList(TerminalFlowMonth terminalFlowMonth) {
        return terminalFlowMonthMapper.selectTerminalFlowMonthList(terminalFlowMonth);
    }

    /**
     * 根据tusn查询终端按月流量信息列表
     *
     * @param tusn 终端序列号
     * @return 终端按月流量信息列表
     */
    @Override
    public List<TerminalFlowMonth> selectTerminalFlowMonthByTusn(String tusn) {
        return terminalFlowMonthMapper.selectTerminalFlowMonthByTusn(tusn);
    }
}
