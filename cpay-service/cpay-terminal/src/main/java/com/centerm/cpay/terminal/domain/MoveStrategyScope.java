package com.centerm.cpay.terminal.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import org.miser.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

import org.miser.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 移机策略范围适配 对象 coms_move_strategy_scope
 *
 * <AUTHOR>
 * @date 2020-01-06
 */
@ApiModel(value = "移机策略适用范围信息对象")
@Data
@ToString
@TableName("coms_move_strategy_scope")
@Accessors(chain = true)
public class MoveStrategyScope {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "${comment}")
    private Long id;

    /**
     * 策略ID
     */
    @Excel(name = "策略ID")
    @ApiModelProperty(value = "策略ID")
    private Long strategyId;

    /**
     * 适用范围 1-机构  2-终端列表
     */
    @Excel(name = "适用范围 1-机构  2-终端列表")
    @ApiModelProperty(value = "适用范围 1-机构  2-终端列表")
    private Integer scopeType;

    /**
     * 机构ID
     */
    @Excel(name = "机构ID")
    @ApiModelProperty(value = "机构ID")
    private Long deptId;

    /**
     * 终端序列号
     */
    @Excel(name = "终端序列号")
    @ApiModelProperty(value = "终端序列号")
    private String tusn;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 更新时间
     */
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}
