package com.centerm.cpay.terminal.mapper;

import com.centerm.cpay.terminal.domain.TerminalAlarmRankdetail;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 终端类型告警 Mapper接口
 *
 * <AUTHOR>
 * @date 2019-09-02
 */
@Component
public interface TerminalAlarmRankdetailMapper extends BaseMapper<TerminalAlarmRankdetail> {

    /**
     * 通过终端类型Id删除终端型号告警信息
     *
     * @param termTypeId 终端类型Id
     * @return 结果
     */
    public int deleteByTermTypeId(Long termTypeId);

    /**
     * 根据条件分页查询终端型号告警信息列表
     *
     * @param terminalAlarmRankdetail 终端型号告警信息
     * @return 终端型号告警信息列表
     */
    public List<TerminalAlarmRankdetail> selectExistAlarmSet(TerminalAlarmRankdetail terminalAlarmRankdetail);

    /**
     * 通过终端类型Id查询终端型号告警信息
     *
     * @param termTypeId 终端类型id
     * @param rank       终端告警级别
     * @return 终端告警型号信息
     */
    public TerminalAlarmRankdetail selectByTermTypeId(@Param("termTypeId") Long termTypeId, @Param("rank") int rank);
}