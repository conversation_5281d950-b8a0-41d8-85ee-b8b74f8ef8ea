package com.centerm.cpay.terminal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.centerm.cpay.common.domain.terminal.TerminalType;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 终端型号 Mapper接口
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
@Component
public interface TerminalTypeMapper extends BaseMapper<TerminalType> {

    /**
     * 根据条件分页查询终端类型信息列表
     *
     * @param terminalType 终端类型信息
     * @return 终端类型集合信息
     */
    public List<TerminalType> selectTerminalTypeByCondition(TerminalType terminalType);

    /**
     * 根据条件分页查询终端类型信息列表
     *
     * @param id 终端类型信息
     * @return 终端类型集合信息
     * <AUTHOR>
     * @date 2019-10-31
     */
    public TerminalType selectTerminalTypeById(@Param("id") Long id);

    /**
     * 根据条件分页查询终端类型信息列表
     *
     * @param code 终端类型信息
     * @return 终端类型集合信息
     * <AUTHOR>
     * @date 2019-10-31
     */
    public TerminalType selectTerminalTypeByCode(@Param("code") String code);

    /**
     * 根据ids查询终端类型
     *
     * @param terminalTypeIds 终端类型ids
     * @return 终端类型集合信息
     */
    List<TerminalType> selectTerminalTypeByIds(@Param("terminalTypeIds") List<Long> terminalTypeIds);

    TerminalType selectTerminalTypeByNameAndFirmId(@Param("terminalType") String terminalType, @Param("firmId") Long firmId);
}