package com.centerm.cpay.terminal.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.cpay.common.domain.terminal.TerminalSysdetail;
import com.centerm.cpay.common.domain.terminal.TerminalSysdetailSta;
import com.centerm.cpay.common.dto.terminal.TerminalTypeStatistics;
import com.centerm.cpay.terminal.mapper.TerminalSysdetailMapper;
import com.centerm.cpay.terminal.service.ITerminalSysdetailService;
import org.miser.system.domain.SysDept;
import org.miser.system.mapper.SysDeptMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 终端系统详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2019-09-18
 */
@Service
public class TerminalSysdetailServiceImpl extends ServiceImpl<TerminalSysdetailMapper, TerminalSysdetail> implements ITerminalSysdetailService {

    @Autowired
    private TerminalSysdetailMapper terminalSysdetailMapper;

    @Autowired
    private SysDeptMapper deptMapper;
    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 根据条件查询终端系统信息列表
     *
     * @param terminalSysdetail 终端系统信息
     * @return 终端系统信息列表
     */
    @Override
    public List<TerminalSysdetail> selectTerminalSysdetailList(TerminalSysdetail terminalSysdetail) {
        return terminalSysdetailMapper.selectTerminalSysdetailList(terminalSysdetail);
    }

    /**
     * 模糊查询终端详情
     *
     * @param terminalSysdetail 终端详情查询
     * @return 结果
     */
    @Override
    public List<TerminalSysdetail> selectByParam(TerminalSysdetail terminalSysdetail) {
        return baseMapper.selectByParam(terminalSysdetail);
    }

    /**
     * 根据终端号查询终端详情
     *
     * @param terminalSysdetail 终端详情查询
     * @return 结果
     */
    @Override
    public TerminalSysdetail selectByTusn(TerminalSysdetail terminalSysdetail) {
        return baseMapper.selectByTusn(terminalSysdetail);
    }

    /**
     * 根据tusn查询终端详情
     *
     * @param tusn 终端序列号
     * @return 结果
     */
    @Override
    public TerminalSysdetail selectTerminalSysdetailByTusn(String tusn) {
        return baseMapper.selectTerminalSysdetailByTusn(tusn);
    }

    @Override
    public void terminalSysDetailStatisticsByDay() {
        List<SysDept> sysDeptsList = deptMapper.selectDeptList(new SysDept());
        for (SysDept sysDept : sysDeptsList) {
            List<TerminalSysdetailSta> terminalSysdetailStaList = baseMapper.terminalSysDetailVersionStatisticsList(sysDept.getDeptId());
            List<TerminalSysdetailSta> filteredList = terminalSysdetailStaList.stream()
                    .filter(item -> item.getVersion() != null && !item.getVersion().isEmpty())
                    .collect(Collectors.toList());
            redisTemplate.opsForValue().set("index_current_terminal_sys_detal_count_"+sysDept.getDeptId(), filteredList.stream()
                    .map(TerminalSysdetailSta::getCount)
                    .collect(Collectors.toList()), 60, TimeUnit.MINUTES);
            redisTemplate.opsForValue().set("index_current_terminal_sys_detal_version_"+sysDept.getDeptId(), filteredList.stream()
                    .map(TerminalSysdetailSta::getVersion)
                    .collect(Collectors.toList()), 60, TimeUnit.MINUTES);
        }
    }
}
