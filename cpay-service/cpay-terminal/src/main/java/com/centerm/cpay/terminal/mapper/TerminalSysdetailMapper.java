package com.centerm.cpay.terminal.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.centerm.cpay.common.domain.terminal.TerminalSysdetail;
import com.centerm.cpay.common.domain.terminal.TerminalSysdetailSta;
import org.apache.ibatis.annotations.Param;
import org.miser.common.annotation.PermissionData;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 终端系统详情 Mapper接口
 *
 * <AUTHOR>
 * @date 2019-09-18
 */
@Repository
public interface TerminalSysdetailMapper extends BaseMapper<TerminalSysdetail> {

    /**
     * 根据条件查询终端系统信息列表
     *
     * @param terminalSysdetail 终端系统信息
     * @return 终端系统信息列表
     */
    @PermissionData(alias = "a")
    public List<TerminalSysdetail> selectTerminalSysdetailList(TerminalSysdetail terminalSysdetail);

    /**
     * 模糊终端详情信息
     *
     * @param terminalSysdetail 终端详情信息
     * @return 结果
     */
    @PermissionData(alias = "a")
    public List<TerminalSysdetail> selectByParam(TerminalSysdetail terminalSysdetail);

    /**
     * 根据终端号查询终端详情
     *
     * @param terminalSysdetail 终端详情信息
     * @return 结果
     */
    public TerminalSysdetail selectByTusn(TerminalSysdetail terminalSysdetail);

    /**
     * 根据tusn查询终端详情
     *
     * @param tusn 终端序列号
     * @return 结果
     */
    public TerminalSysdetail selectTerminalSysdetailByTusn(@Param("tusn") String tusn);

    List<TerminalSysdetailSta> terminalSysDetailVersionStatisticsList(@Param("deptId") Long deptId);
}