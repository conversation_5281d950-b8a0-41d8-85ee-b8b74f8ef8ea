package com.centerm.cpay.terminal.mapper;

import com.centerm.cpay.common.domain.terminal.TerminalInfo;
import com.centerm.cpay.common.domain.terminal.TerminalUpdate;
import com.centerm.cpay.common.dto.terminal.*;
import org.apache.ibatis.annotations.Param;
import org.miser.common.annotation.PermissionData;
import org.miser.common.core.service.MyBaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 终端信息 Mapper接口
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
@Repository
public interface TerminalInfoMapper extends MyBaseMapper<TerminalInfo> {

    /**
     * 根据条件查询终端信息列表
     *
     * @param terminalInfoInOutput 终端信息
     * @return 终端信息列表
     */
    @PermissionData(alias = "a")
    public List<TerminalInfoInOutput> selectTerminalInfoList(TerminalInfoInOutput terminalInfoInOutput);

    /**
     *【查询】机构下终端数目
     * @param deptId 机构号
     * @return 终端数目
     */
    int selectDeviceCount(Long deptId);

    /**
     * 根据条件查询终端信息列表
     *
     * @param terminalInfoInOutput 终端信息
     * @return 终端信息列表
     */
    //@PermissionData(alias = "a") //group by 冲突
    public List<TerminalInfoExport> selectTerminalInfoListExport(TerminalInfoInOutput terminalInfoInOutput);

    /**
     * 根据条件查询是否分组终端信息列表
     *
     * @param terminalInfoSetGroupInput 终端信息
     * @return 终端信息列表
     */
    @PermissionData(alias = "a")
    public List<TerminalInfoInOutput> selectTerminalInfoListByIsGroup(TerminalInfoSetGroupInput terminalInfoSetGroupInput);

    /**
     * 根据条件查询终端信息列表
     *
     * @param terminalInfo 终端信息
     * @return 终端信息列表
     */
    @PermissionData(alias = "a")
    public List<TerminalInfo> selectTerminalInfoListByCondition(TerminalInfo terminalInfo);

    /**
     * 根据机构编码查询终端序列号
     *
     * @param deptId 机构编码
     * @return 终端序列号
     */
    public List<String> selectTusnBySysDeptId(@Param("deptId") Long deptId);

    /**
     * 根据组别id查询终端信息列表
     *
     * @param terminalInfoInOutput 终端信息
     * @return 终端信息列表
     */
    @PermissionData(alias = "a")
    public List<TerminalInfoInOutput> selectTerminalInfoListByGroupId(TerminalInfoInOutput terminalInfoInOutput);

    /**
     * 根据组别id查询终端分组信息列表
     *
     * @param terminalInfoInOutput 终端信息
     * @return 终端分组信息列表
     */
    @PermissionData(alias = "a")
    public List<TerminalInfoInOutput> selectTerminalInfoNoGroupListByGroupId(TerminalInfoInOutput terminalInfoInOutput);

    /**
     * 根据tusn查询终端信息数据
     *
     * @param tusn 终端号
     * @return 结果
     */
    public TerminalInfo selectTerminalInfoByTusn(@Param("tusn") String tusn);


    /**
     * 根据机构查询终端号
     *
     * @param terminalInfo 终端信息
     * @return : tusn列表
     * <AUTHOR> nff
     * @date : 2019/10/11 14:57
     */
    List<String> selectTusnByInsGroup(TerminalInfo terminalInfo);

    /**
     * 根据机构查询终端号
     *
     * @param terminalInfo 终端信息
     * @return : tusn列表
     * <AUTHOR> nff
     * @date : 2019/10/11 14:57
     */
    List<String> selectTusnByInsGroupInOutput(TerminalInfoInOutput terminalInfo);


    /**
     * 根据机构查询终端号
     *
     * @param terminalInfo 终端信息
     * @return : tusn列表
     * <AUTHOR> nff
     * @date : 2019/10/11 14:57
     */
    List<String> selectTerminalIdNoGroupList(TerminalInfoInOutput terminalInfo);

    /**
     * 根据id查询终端信息列表
     *
     * @param id 终端id
     * @return 终端信息
     */
    public TerminalInfoInOutput getTerminalInfoById(@Param("id") Long id);


    /**
     * 查询12个月内终端统计数据
     * @return 终端统计数据
     * <AUTHOR>
     */
    @PermissionData(alias = "fti")
    public List<TerminalStatistics> selectTerminalStatisticsForTwelveMonths();

    /**
     * 更新终端网络状态
     * @param terminalInfo
     * @return
     * <AUTHOR>
     * @created 20211117
     */
    public boolean updateTerminaNetworkStatusByTusn(TerminalInfo terminalInfo);

    boolean updateTerminaBaseInfo(TerminalInfo terminalInfo);

    int selectDeviceCountByCondition(TerminalInfo terminalInfo);

    List<TerminalTypeStatistics> selectDeviceCountByGroupType(TerminalInfo terminalInfo);


    List<TerminalInfo> selectByTusns(@Param("tusns") List<String> tusns);

    List<TerminalInfo> selectByTusnsAndDeptId(TerminalUpdate terminalUpdate);

    int batchInsert(@Param("list") List<TerminalInfo> terminalInfoList);

    int updateDeptIdByTusns(TerminalUpdate terminalUpdate);
}