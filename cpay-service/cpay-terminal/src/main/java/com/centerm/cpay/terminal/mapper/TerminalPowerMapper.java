package com.centerm.cpay.terminal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.centerm.cpay.common.domain.terminal.TerminalPower;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 终端开关机信息 Mapper接口
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
public interface TerminalPowerMapper extends BaseMapper<TerminalPower> {

    /**
     * 根据条件查询终端开关机信息列表
     *
     * @param terminalPower 终端开关机信息
     * @return 终端开关机信息列表
     */
    public List<TerminalPower> selectTerminalPowerList(TerminalPower terminalPower);

    /**
     * 根据tusn查询终端开关机信息列表
     *
     * @param tusn 终端序列号
     * @return 终端开关机信息列表
     */
    public List<TerminalPower> selectTerminalPowerByTusn(@Param("tusn") String tusn);
}