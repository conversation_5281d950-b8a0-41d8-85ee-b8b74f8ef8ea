package com.centerm.cpay.terminal.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.centerm.cpay.common.domain.terminal.TerminalDynInfo;
import com.centerm.cpay.common.domain.terminal.TerminalInfo;
import com.centerm.cpay.common.domain.terminal.TerminalParam;
import com.centerm.cpay.terminal.mapper.TerminalParamMapper;
import com.centerm.cpay.terminal.service.ITerminalDynInfoService;
import com.centerm.cpay.terminal.service.ITerminalInfoService;
import com.centerm.cpay.terminal.service.ITerminalParamService;
import lombok.extern.slf4j.Slf4j;
import org.miser.common.core.domain.BaseInput;
import org.miser.common.core.service.BaseServiceImpl;
import org.miser.common.utils.data.ApiAssert;
import org.miser.common.utils.data.CommonUtils;
import org.miser.common.utils.data.CtUtils;
import org.miser.common.utils.data.StringUtils;
import org.miser.system.domain.SysDept;
import org.miser.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 终端参数信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
@Slf4j
@Service
public class TerminalParamServiceImpl extends BaseServiceImpl<TerminalParamMapper, TerminalParam> implements ITerminalParamService {

    /**
     *【Redis：key】终端在线过期
     */
    private static final String ONLINE_EXPIRE_KEY = "online:expire:";

    @Autowired
    private ITerminalDynInfoService terminalDynInfoService;

    @Autowired
    private ITerminalParamService terminalParamService;

    @Autowired
    private ITerminalInfoService terminalInfoService;

    @Autowired
    private ISysDeptService deptService;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private ITerminalParamService paramService;

    /**
     * 根据条件分页查询终端参数信息列表
     *
     * @param terminalParam 终端参数信息
     * @return 终端参数集合信息
     */
    @Override
    public List<TerminalParam> selectTerminalParamListByCondition(TerminalParam terminalParam) {
        return baseMapper.selectTerminalParamListByCondition(terminalParam);
    }

    /**
     * 根据参数id查询终端参数信息
     *
     * @param id 参数id
     * @return 终端参数信息
     */
    @Override
    public TerminalParam getTerminalParamById(Long id) {
        return baseMapper.getTerminalParamById(id);
    }

    @Override
    public Boolean hasParamDownloadTask(BaseInput<TerminalInfo> baseInput, String paramVersion) {
        //保存终端动态信息
        saveTerminalDynInfo(baseInput.getTusn(), paramVersion);
        TerminalParam terminalParam = getTerminalParamByTusn(baseInput);
        log.info("查询终端[{}]参数版本:[{}],终端信息版本：[{}]", baseInput.getTusn(),
                terminalParam == null ? null : terminalParam.getParamVersion(), paramVersion);
        boolean hasTask = terminalParam != null && !terminalParam.getParamVersion().equals(paramVersion);
        log.info("\n是否有参数下载任务：{}", hasTask);
        return hasTask;
    }

    private TerminalDynInfo saveTerminalDynInfo(String tusn, String paramVersion) {
        TerminalDynInfo terminalDynInfo = new TerminalDynInfo();
        terminalDynInfo.setParaVersion(paramVersion);
        terminalDynInfo.setTusn(tusn);
        terminalDynInfo.setLatestAccessTime(CtUtils.getCurrentTime());
        terminalDynInfoService.saveOrUpdate(terminalDynInfo, new QueryWrapper<TerminalDynInfo>()
                .lambda().eq(TerminalDynInfo::getTusn, tusn));
        return terminalDynInfo;
    }

    @Override
    public TerminalParam getTerminalParamByTusn(BaseInput<TerminalInfo> baseInput) {
        //查询终端组别是否有参数
        TerminalInfo terminalInfo = terminalInfoService.selectTerminalInfoByTusn(baseInput.getTusn());
        TerminalParam terminalParam = baseMapper.selectParamByGroup(terminalInfo.getId());
        if (terminalParam == null || StringUtils.isEmpty(terminalParam.getParamContent())) {
            SysDept dept = deptService.selectDeptById(terminalInfo.getDeptId());
            //查询终端本机构及所有父机构参数
            List<TerminalParam> terminalParamList = baseMapper.selectParamByDept(dept.getAncestors()+","+dept.getDeptId());
            ApiAssert.notEmpty("", terminalParamList);
            terminalParam = terminalParamList.get(0);
        }
        return terminalParam;
    }
    @Override
    public TerminalParam getTerminalParamByTerminalInfo(TerminalInfo terminalInfo) {
        //查询终端组别是否有参数

        TerminalParam terminalParam = baseMapper.selectParamByGroup(terminalInfo.getId());
        if (terminalParam == null || StringUtils.isEmpty(terminalParam.getParamContent())) {
            SysDept dept = deptService.selectDeptById(terminalInfo.getDeptId());
            //查询终端本机构及所有父机构参数
            List<TerminalParam> terminalParamList = baseMapper.selectParamByDept(dept.getAncestors()+","+dept.getDeptId());
            ApiAssert.notEmpty("", terminalParamList);
            terminalParam = terminalParamList.get(0);
        }
        return terminalParam;
    }

    @Override
    public void expireOnlineStatus(TerminalInfo terminalInfo) {
        TerminalParam param = paramService.getTerminalParamByTerminalInfo(terminalInfo);
        if (CommonUtils.isNotEmpty(param)) {
            //【读取】心跳周期时间(存在配置参数：过期时间=心跳*1.5倍时间)
            String paramContent = param.getParamContent();
            Long expiration = Optional.ofNullable(paramContent)
                    .map(JSONObject::parseObject)
                    .map(object -> object.getString("htIntvl"))
                    .map(Long::parseLong)
                    .map(expire -> expire * 2)
                    .orElse(5400L);

            //【更新】Redis过期时间(存在配置参数：心跳*1.5倍时间)
            redisTemplate.opsForValue().set(ONLINE_EXPIRE_KEY + terminalInfo.getTusn(),
                    StringUtils.EMPTY, expiration, TimeUnit.SECONDS);
        } else {
            //【更新】Redis过期时间(不存在配置参数：默认1.5小时)
            redisTemplate.opsForValue().set(ONLINE_EXPIRE_KEY + terminalInfo.getTusn(),
                    StringUtils.EMPTY, 5400L, TimeUnit.SECONDS);
        }
    }

    @Override
    public TerminalParam selectTerminalParam(TerminalParam terminalParam) {
        return baseMapper.selectTerminalParam(terminalParam);
    }

    @Override
    public List<TerminalParam> selectParamByDept(String ancestors) {
        return baseMapper.selectParamByDept(ancestors);
    }
}