package com.centerm.cpay.terminal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.centerm.cpay.common.domain.terminal.TerminalDynInfo;
import com.centerm.cpay.common.dto.terminal.TerminalDynPositionInfo;
import org.miser.common.annotation.PermissionData;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 终端动态信息Mapper接口
 *
 * <AUTHOR>
 * @date 2019-11-28
 */
@Repository
public interface TerminalDynInfoMapper extends BaseMapper<TerminalDynInfo> {
    /**
     * 查询终端动态信息
     *
     * @param id 终端动态信息ID
     * @return 终端动态信息
     */
    public TerminalDynInfo selectTerminalDynInfoById(Long id);

    /**
     * 查询终端动态信息列表
     *
     * @param terminalDynInfo 终端动态信息
     * @return 终端动态信息集合
     */
    @PermissionData(alias = "t")
    public List<TerminalDynInfo> selectTerminalDynInfoList(TerminalDynInfo terminalDynInfo);
    /**
     * 查询终端动态信息列表
     *
     * @param terminalDynInfo 终端动态信息
     * @return 终端动态信息集合
     */
    public List<TerminalDynPositionInfo> selectAllTerminalPosition(TerminalDynInfo terminalDynInfo);


}
