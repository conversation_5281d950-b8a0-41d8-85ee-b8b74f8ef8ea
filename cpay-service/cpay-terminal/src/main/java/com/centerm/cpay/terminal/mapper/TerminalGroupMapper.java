package com.centerm.cpay.terminal.mapper;

import com.centerm.cpay.common.domain.terminal.TerminalGroup;
import org.apache.ibatis.annotations.Param;
import org.miser.common.annotation.PermissionData;
import org.miser.common.core.service.MyBaseMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 终端组别 Mapper接口
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
@Repository
public interface TerminalGroupMapper extends MyBaseMapper<TerminalGroup> {

    /**
     * 根据条件查询终端组别信息列表
     *
     * @param terminalGroup 终端组别信息
     * @return 终端组别信息信息
     */
    @PermissionData(alias = "b")
    public List<TerminalGroup> selectTerminalGroupListByCondition(TerminalGroup terminalGroup);

    /**
     * 根据终端id查询终端组别信息列表
     *
     * @param terminalId 终端组别id
     * @return 终端组别信息列表
     */
    @PermissionData(alias = "a")
    public List<TerminalGroup> getGroupByTerminalId(Integer terminalId);

    /**
     * 根据组别id查询终端组别信息
     *
     * @param id 组别id
     * @return 终端组别信息
     */
    @PermissionData(alias = "a")
    public TerminalGroup getTerminalGroupById(@Param("id") Long id);
}