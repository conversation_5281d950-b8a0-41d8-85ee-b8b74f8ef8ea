package com.centerm.cpay.terminal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.centerm.cpay.common.domain.terminal.TerminalMoveRecord;
import com.centerm.cpay.terminal.mapper.TerminalMoveRecordMapper;
import com.centerm.cpay.terminal.service.ITerminalMoveRecordService;
import org.springframework.stereotype.Service;

/**
 * 终端移机记录 Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-01-06
 */
@Service
public class TerminalMoveRecordServiceImpl extends ServiceImpl<TerminalMoveRecordMapper, TerminalMoveRecord> implements ITerminalMoveRecordService {


    /**
     * 查询终端移机记录
     *
     * @param id 终端移机记录 ID
     * @return 终端移机记录
     */
    @Override
    public TerminalMoveRecord selectTerminalMoveRecordById(Long id) {
        return baseMapper.selectTerminalMoveRecordById(id);
    }

    /**
     * 查询终端移机记录 列表
     *
     * @param terminalMoveRecord 终端移机记录
     * @return 终端移机记录
     */
    @Override
    public List<TerminalMoveRecord> selectTerminalMoveRecordList(TerminalMoveRecord terminalMoveRecord) {
        return baseMapper.selectTerminalMoveRecordList(terminalMoveRecord);
    }

    @Override
    public boolean updateTerminalMoveRecordToLockStatusByIds(List<Long> ids) {
        return SqlHelper.retBool(baseMapper.updateTerminalMoveRecordToLockStatusByIds(ids));
    }

    @Override
    public boolean updateTerminalMoveRecordToUnlockStatusByIds(List<Long> ids) {
        return SqlHelper.retBool(baseMapper.updateTerminalMoveRecordToUnlockStatusByIds(ids));
    }


}
