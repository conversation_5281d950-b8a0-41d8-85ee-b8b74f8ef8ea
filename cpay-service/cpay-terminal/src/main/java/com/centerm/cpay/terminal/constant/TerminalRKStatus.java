package com.centerm.cpay.terminal.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> @version 1.0
 * @date
 */
public enum TerminalRKStatus {
    TERM_STATUS_YRK("0", "已入库"),

    TERM_STATUS_YCK("1", "已出库"),

    TERM_STATUS_YBF("2", "已报废"),
    ;

    /**
     * 编码
     */
    private String code;

    /**
     * 描述
     */
    private String text;

    /**
     * 构造函数
     *
     * @param code
     * @param text
     */
    TerminalRKStatus(String code, String text) {
        this.code = code;
        this.text = text;
    }

    public String getCode() {
        return code;
    }

    public String getText() {
        return text;
    }

    /**
     * 根据编码返回枚举值
     *
     * @param code
     * @return
     */
    public static TerminalRKStatus getEnums(String code) {
        for (TerminalRKStatus enums : values()) {
            if (StringUtils.equalsIgnoreCase(code, enums.getCode())) {
                return enums;
            }
        }
        return null;
    }
}
