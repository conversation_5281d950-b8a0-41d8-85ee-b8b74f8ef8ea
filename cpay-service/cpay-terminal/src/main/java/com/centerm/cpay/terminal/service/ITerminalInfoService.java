package com.centerm.cpay.terminal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.centerm.cpay.common.domain.terminal.TerminalInfo;
import com.centerm.cpay.common.dto.terminal.*;
import org.miser.common.core.domain.ExcelImportResult;
import org.miser.common.core.service.IBaseService;
import org.miser.system.domain.SysUser;

import java.util.List;

/**
 * 终端信息 Service接口
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
public interface ITerminalInfoService extends IBaseService<TerminalInfo>, IService<TerminalInfo> {

    /**
     * 根据条件查询终端信息列表
     *
     * @param terminalInfoInOutput 终端信息
     * @return 终端信息列表
     */
    public List<TerminalInfoInOutput> selectTerminalInfoList(TerminalInfoInOutput terminalInfoInOutput);

    /**
     *【查询】机构下终端数目
     * @param deptId 机构号
     * @return 终端数目
     */
    int selectDeviceCount(Long deptId);

    /**
     * 根据条件查询终端信息列表export
     *
     * @param terminalInfoInOutput 终端信息
     * @return 终端信息列表
     */
    public List<TerminalInfoExport> selectTerminalInfoListExport(TerminalInfoInOutput terminalInfoInOutput);

    /**
     * 根据机构编码查询终端序列号
     *
     * @param deptId 机构id
     * @return 终端序列号
     */
    public List<String> selectTusnBySysDeptId(Long deptId);


    /**
     * 根据组别id查询终端信息列表
     *
     * @param terminalInfoInOutput 终端信息
     * @return 终端信息列表
     */
    public List<TerminalInfoInOutput> selectTerminalInfoListByGroupId(TerminalInfoInOutput terminalInfoInOutput);

    /**
     * 根据组别id查询终端分组信息列表
     *
     * @param terminalInfoInOutput 终端信息
     * @return 终端分组信息列表
     */
    public List<TerminalInfoInOutput> selectTerminalInfoNoGroupListByGroupId(TerminalInfoInOutput terminalInfoInOutput);


    /**
     * 导入终端信息数据
     *
     * @param excelTerminalInfoImportList 终端信息数据列表
     * @return 结果
     */
    public ExcelImportResult importTerminalInfo(ExcelImportResult<TerminalInfoImport> excelTerminalInfoImportList,boolean updateSupport, String deptId, String groupId, String firmId, String terminalTypes, String operName, Long operDeptId);


    /**
     * 根据机构查询终端号
     *
     * @param terminalInfo 终端信息
     * @return : tusn列表
     * <AUTHOR> nff
     * @date : 2019/10/11 14:57
     */
    List<String> selectTusnByInsGroup(TerminalInfo terminalInfo);

    /**
     * 根据机构查询终端号
     *
     * @param terminalInfo 终端信息
     * @return : tusn列表
     * <AUTHOR> nff
     * @date : 2019/10/11 14:57
     */
    List<String> selectTusnByInsGroup(TerminalInfoInOutput terminalInfo);


    /**
     * 根据组别id查询终端分组信息列表
     *
     * @param terminalInfoInOutput 终端信息
     * @return 终端分组信息列表
     */
    List<String> selectTerminalIdNoGroupList(TerminalInfoInOutput terminalInfoInOutput);


    /**
     * 根据id查询终端信息列表
     *
     * @param id 终端id
     * @return 终端信息
     */
    public TerminalInfoInOutput getTerminalInfoById(Long id);

    /**
     * 根据条件查询是否分组的终端信息列表
     *
     * @param terminalInfoSetGroupInput 终端信息
     * @return 终端信息列表
     */
    List<TerminalInfoInOutput> selectTerminalInfoListByIsGroup(TerminalInfoSetGroupInput terminalInfoSetGroupInput);


    /**
     * 根据Tusn查询终端信息表
     *
     * @param tusn 终端tusn
     * @return 终端信息
     */
    TerminalInfo selectTerminalInfoByTusn(String tusn);

    /**
     * 查询12个月内终端统计数据
     * @return 终端统计数据
     * <AUTHOR>
     */
    List<TerminalStatistics> selectTerminalStatisticsForTwelveMonths();

    /**
     * 更新终端网络状态
     * @param terminalInfo
     * @return
     * <AUTHOR>
     * @created 20211117
     */
    public boolean updateTerminaNetworkStatusByTusn(TerminalInfo terminalInfo);

    boolean updateTerminaBaseInfo(TerminalInfo terminalInfo);

    Boolean insert(TerminalInfoInput terminalInfoInput, SysUser loginUser);

    void terminaCurrentlStatisticsByDay();

    void terminalActiveStatisticsByDay();

}