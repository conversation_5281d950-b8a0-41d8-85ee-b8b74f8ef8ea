package com.centerm.cpay.terminal.service.impl;

import com.centerm.cpay.common.domain.terminal.TerminalType;
import com.centerm.cpay.terminal.constant.TerminalConstants;
import org.springframework.stereotype.Service;
import com.centerm.cpay.terminal.mapper.TerminalAlarmRankdetailMapper;
import com.centerm.cpay.terminal.domain.TerminalAlarmRankdetail;
import com.centerm.cpay.terminal.service.ITerminalAlarmRankdetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 终端类型告警Service业务层处理
 *
 * <AUTHOR>
 * @date 2019-09-02
 */
@Service
public class TerminalAlarmRankdetailServiceImpl extends ServiceImpl<TerminalAlarmRankdetailMapper, TerminalAlarmRankdetail> implements ITerminalAlarmRankdetailService {

    /**
     * 通过终端类型Id修改保存终端型号告警
     *
     * @param terminalType 终端类型
     * @return 结果
     */
    @Override
    public boolean editSaveByTerminalType(TerminalType terminalType) {
        TerminalAlarmRankdetail terminalAlarmRankdetail = baseMapper.selectByTermTypeId(terminalType.getId().longValue(), TerminalConstants.TERMINAL_ALARM_RANK_HIGH);
        if (terminalAlarmRankdetail != null) {
            terminalAlarmRankdetail.setDetail(terminalType.getIdsInput());
            baseMapper.updateById(terminalAlarmRankdetail);
            return true;
        }
        return false;
    }
}
