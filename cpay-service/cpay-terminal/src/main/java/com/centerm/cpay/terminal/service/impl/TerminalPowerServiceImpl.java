package com.centerm.cpay.terminal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.cpay.common.domain.terminal.TerminalPower;
import com.centerm.cpay.terminal.mapper.TerminalPowerMapper;
import com.centerm.cpay.terminal.service.ITerminalPowerService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 终端开关机信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
@Service
public class TerminalPowerServiceImpl extends ServiceImpl<TerminalPowerMapper, TerminalPower> implements ITerminalPowerService {

    /**
     * 根据条件查询终端开关机信息列表
     *
     * @param terminalPower 终端开关机信息
     * @return 终端开关机信息列表
     */
    @Override
    public List<TerminalPower> selectTerminalPowerList(TerminalPower terminalPower) {
        return baseMapper.selectTerminalPowerList(terminalPower);
    }

    /**
     * 根据tusn查询终端开关机信息列表
     *
     * @param tusn 终端序列号
     * @return 终端开关机信息列表
     */
    @Override
    public List<TerminalPower> selectTerminalPowerByTusn(String tusn) {
        return baseMapper.selectTerminalPowerByTusn(tusn);
    }
}
