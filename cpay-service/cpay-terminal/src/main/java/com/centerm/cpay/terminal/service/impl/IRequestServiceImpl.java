package com.centerm.cpay.terminal.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.centerm.cpay.common.domain.terminal.TerminalFirm;
import com.centerm.cpay.common.domain.terminal.TerminalInfo;
import com.centerm.cpay.terminal.service.ITerminalDynInfoService;
import com.centerm.cpay.terminal.service.ITerminalFirmService;
import com.centerm.cpay.terminal.service.ITerminalInfoService;
import org.miser.common.core.domain.BaseInput;
import org.miser.common.service.IRequestService;
import org.miser.common.utils.data.ApiAssert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 获取终端秘钥匙实现类
 *
 * <AUTHOR>
 * @date 2019/09/30 09:55
 **/
@Service
public class IRequestServiceImpl implements IRequestService<TerminalInfo> {

    @Autowired
    private ITerminalFirmService terminalFirmService;

    @Autowired
    private ITerminalInfoService terminalInfoService;


    @Autowired
    private ITerminalDynInfoService terminalDynInfoService;


    @Override
    public String getPublicKey(String tusn) {
        TerminalFirm terminalFirm = terminalFirmService.selectFirmByTerminalSn(tusn);
        return terminalFirm == null ? null : terminalFirm.getPublicKey();
    }

    @Override
    public String getPublicKey(BaseInput<TerminalInfo> baseInput) {
        //更新终端动态信息
        terminalDynInfoService.updateByBaseInput(baseInput);
        //设置终端信息
        TerminalInfo terminalInfo = terminalInfoService.getOne(new QueryWrapper<TerminalInfo>().lambda().eq(TerminalInfo::getTusn, baseInput.getTusn()));
        ApiAssert.isTrue("terminalID.not.exists", terminalInfo != null, baseInput.getTusn());
        /*ApiAssert.isTrue("terminal.not.activate", TerminalStatusEnum.CONNECTED.getCode()
                        .equals(terminalInfo.getStatus()),
                baseInput.getTusn()
        );*/
        baseInput.setT(terminalInfo);
        return getPublicKey(baseInput.getTusn());
    }
}
