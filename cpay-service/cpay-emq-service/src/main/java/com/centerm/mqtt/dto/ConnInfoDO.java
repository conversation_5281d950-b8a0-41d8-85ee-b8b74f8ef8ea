package com.centerm.mqtt.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("cpayiot_conn_info")
public class ConnInfoDO implements Serializable{

    private static final long serialVersionUID = 659148189819102283L;

    @TableId
    private Long id;
    
    private String connAddr;
    
    private String serverPort;
    
    private String isSslOn;
    
    private String connInfoDetail;
}