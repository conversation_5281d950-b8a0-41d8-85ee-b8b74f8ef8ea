package com.centerm.mqtt.utils;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/7/16 10:08
 */
public class HexByte {

    public static String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder();
        if (src == null || src.length <= 0) {
            return null;
        }
        for (byte b : src) {
            int v = b & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString().toUpperCase();
    }

    public static byte[] hexStrToBytes(String str) {
        // 如果字符串长度不为偶数，则追加0
        if (str.length() % 2 != 0) {
            str = "0" + str;
        }

        byte[] b = new byte[str.length() / 2];
        byte c1, c2;
        for (int y = 0, x = 0; x < str.length(); ++y, ++x) {
            c1 = (byte) str.charAt(x);
            if (c1 > 0x60)
                c1 -= 0x57;
            else if (c1 > 0x40)
                c1 -= 0x37;
            else
                c1 -= 0x30;
            c2 = (byte) str.charAt(++x);
            if (c2 > 0x60)
                c2 -= 0x57;
            else if (c2 > 0x40)
                c2 -= 0x37;
            else
                c2 -= 0x30;
            b[y] = (byte) ((c1 << 4) + c2);
        }
        return b;
    }
}