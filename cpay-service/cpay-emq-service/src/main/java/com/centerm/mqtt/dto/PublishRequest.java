package com.centerm.mqtt.dto;

import com.centerm.mqtt.utils.constants.MsgCenterConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: msg-emq-push
 * @description: 消息发布实体类
 * @author: <PERSON>
 * @create: 2019-02-26 15:45
 **/
@Data
@ApiModel
public class PublishRequest {
    @ApiModelProperty(value = "", required = true)
    private String topic;
    @ApiModelProperty(value = "指定消息的发送方式。0：最多发送一次。1：最少发送一次。如果不传入此参数，则使用默认值0。", required = false)
    private Integer qos = MsgCenterConstants.QOS;
    @ApiModelProperty(value = "要发送的消息主体。", required = false)
    private String payload;
    @ApiModelProperty(value = "默认为 false", required = false)
    private Boolean retain = false;
    @ApiModelProperty(value = "默认为 ‘http’", required = false)
    private String clientId = "http";
}
