package com.centerm.mqtt.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: msg-center
 * @description: 指令
 * @author: <PERSON>
 * @create: 2019/3/14 11:00
 **/
@Data
@ApiModel
public class Instruct extends BaseDTO implements Serializable {

    @ApiModelProperty(value = "设备SN号", required = true)
    private String sn;

    @ApiModelProperty(value = "运维指令", required = true)
    private String command;

    private String taskId;

}
