package com.centerm.mqtt.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: basic
 * @description: 请求数据列表信息
 * @author: <PERSON>
 * @create: 2019-02-27 13:55
 **/
@Data
@ApiModel
public class QueryList {
    @ApiModelProperty(value = "指定返回结果中每页显示的记录数量，最大值是50。默认值是10。", required = false)
    private Integer pageSize;
    @ApiModelProperty(value = "指定从返回结果中的第几页开始显示。默认值是 1。", required = false)
    private Integer currentPage;
    @ApiModelProperty(value = "要查询的设备所隶属的产品Key。", required = false)
    private String sign;
}
