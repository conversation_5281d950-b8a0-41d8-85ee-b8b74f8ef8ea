package com.centerm.cpay.msgcenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.cpay.msgcenter.domain.MqttUser;
import com.centerm.cpay.msgcenter.mapper.AdminMqttUserMapper;
import com.centerm.cpay.msgcenter.service.IMqttUserService;
import org.springframework.stereotype.Service;

/**
 * emq认证user 服务层实现
 *
 * <AUTHOR> auto
 * @date 2019-03-05
 */
@Service
public class MqttUserServiceImpl extends ServiceImpl<AdminMqttUserMapper, MqttUser> implements IMqttUserService {

    @Override
    public boolean deleteByUsername(String username) {
        return getBaseMapper().deleteByusername(username);
    }
}
