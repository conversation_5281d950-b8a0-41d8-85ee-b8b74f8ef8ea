package com.centerm.cpay.msgcenter.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.miser.common.annotation.Excel;

import java.util.Date;

/**
 * emq认证user表 mqtt_user
 *
 * <AUTHOR>
 * @date 2019-03-19
 */
@ApiModel(value = "emq认证user")
@Data
@ToString
@TableName("mqtt_user")
@Accessors(chain = true)
public class MqttUser {

	@ApiModelProperty(value = "编号")
	private Integer id;

	@Excel(name = "Username")
	@ApiModelProperty(value = "用户名")
	private String username;

	@Excel(name = "Password")
	@ApiModelProperty(value = "密码")
	private String password;

	@Excel(name = "Salt")
	@ApiModelProperty(value = "密码盐")
	private String salt;


	@ApiModelProperty(value = "设备SN号")
	private String remark;

	@ApiModelProperty(value = "是否超级用户")
	private Integer isSuperuser;

	@Excel(name = "Create Time", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建时间")
	private Date created;
}