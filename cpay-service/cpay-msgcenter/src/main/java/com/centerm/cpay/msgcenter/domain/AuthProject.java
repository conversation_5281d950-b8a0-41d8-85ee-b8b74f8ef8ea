package com.centerm.cpay.msgcenter.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @created 2021/11/2 上午10:34
 */
@ApiModel(value = "项目授权信息")
@Data
@ToString
@TableName("cpayiot_auth_project")
@Accessors(chain = true)
public class AuthProject {

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "授权接入项目编号")
    private String authProjectId;

    @ApiModelProperty(value = "授权接入项目私钥")
    private String authProjectPrivateKey;

    @ApiModelProperty(value = "授权接入项目公钥")
    private String authProjectPubKey;

    @ApiModelProperty(value = "授权接入项目描述")
    private String authProjectDetail;

    @ApiModelProperty(value = "创建者")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "接入连接信息ID")
    private Long connInfoId;
}
