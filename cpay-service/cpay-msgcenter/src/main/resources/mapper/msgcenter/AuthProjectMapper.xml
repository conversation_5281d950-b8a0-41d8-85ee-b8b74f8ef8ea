<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.cpay.msgcenter.mapper.AdminAuthProjectMapper">
    
    <resultMap type="com.centerm.cpay.msgcenter.domain.AuthProject" id="AuthProjectResult">
        <result property="id" column="id" />
        <result property="authProjectId" column="auth_project_id" />
        <result property="authProjectPrivateKey" column="auth_project_private_key" />
        <result property="authProjectPubKey" column="auth_project_pub_key" />
        <result property="authProjectDetail" column="auth_project_detail" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="connInfoId" column="conn_info_id" />
    </resultMap>

    <insert id="saveAuthProject" parameterType="com.centerm.cpay.msgcenter.domain.AuthProject" useGeneratedKeys="true" keyProperty="id">
        insert into cpayiot_auth_project
        <trim prefix="(" suffix=")" suffixOverrides=",">

          <if test="id != null and id != 0">id,</if>
          <if test="authProjectId != null and authProjectId != ''">auth_project_id,</if>
          <if test="authProjectPrivateKey != null and authProjectPrivateKey != ''">auth_project_private_key,</if>
          <if test="authProjectPubKey != null and authProjectPubKey != ''">auth_project_pub_key,</if>
          <if test="authProjectDetail != null and authProjectDetail != ''">auth_project_detail,</if>
          <if test="createBy != null and createBy != ''">create_by,</if>
          <if test="createTime != null">create_time,</if>
          <if test="updateBy != null and updateBy != ''">update_by,</if>
          <if test="updateTime != null ">update_time,</if>
          conn_info_id
        </trim>

        <trim prefix="values (" suffix=")" suffixOverrides=",">

          <if test="id != null and id != 0">#{id},</if>
          <if test="authProjectId != null and authProjectId != ''">#{authProjectId},</if>

          <if test="authProjectPrivateKey != null and authProjectPrivateKey != ''">#{authProjectPrivateKey},</if>
          <if test="authProjectPubKey != null and authProjectPubKey != ''">#{authProjectPubKey},</if>
          <if test="authProjectDetail != null and authProjectDetail != ''">#{authProjectDetail},</if>
          <if test="createBy != null and createBy != ''">#{createBy},</if>
          sysdate(),
          <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
          sysdate(),
          #{connInfoId}
        </trim>

    </insert>

    <select id="listAuthProject" resultMap="AuthProjectResult" parameterType="com.centerm.cpay.msgcenter.domain.AuthProject">
        select a.id, a.auth_project_id, a.auth_project_private_key, a.auth_project_pub_key, a.auth_project_detail, a.create_by, a.create_time, a.update_by, a.update_time, a.conn_info_id
        from cpayiot_auth_project a
        <where>
            <if test="authProjectId != null and authProjectId != ''">
                a.auth_project_id like concat('%', #{authProjectId}, '%')
            </if>
        </where>
    </select>

    <select id="selectByAuthProjectId" resultMap="AuthProjectResult" parameterType="com.centerm.cpay.msgcenter.domain.AuthProject">
        select a.id, a.auth_project_id, a.auth_project_private_key, a.auth_project_pub_key, a.auth_project_detail, a.create_by, a.create_time, a.update_by, a.update_time, a.conn_info_id
        from cpayiot_auth_project a
        <where>
            <if test="authProjectId != null and authProjectId != ''">
                a.auth_project_id = #{authProjectId}
            </if>
        </where>
    </select>

</mapper>