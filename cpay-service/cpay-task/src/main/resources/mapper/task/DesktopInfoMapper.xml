<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.cpay.task.mapper.DesktopInfoMapper">

    <resultMap type="DesktopInfo" id="DesktopInfoResult">
        <result property="id" column="id"/>
        <result property="devId" column="dev_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="desktopName" column="desktop_name"/>
        <result property="desktopJson" column="desktop_json"/>
        <result property="desktopFileUrl" column="desktop_file_url"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="screenshotUrl" column="screenshot_url"/>
        <result property="imageUrls" column="image_urls"/>
        <result property="animationCycle" column="animation_cycle"/>
    </resultMap>

    <sql id="selectDesktopInfoVo">
        select t.id, t.dev_id, t.dept_id, t.desktop_name, t.desktop_json, t.desktop_file_url, t.is_deleted, t.create_by,
        t.create_time, t.update_by, t.update_time, t.remark, t.screenshot_url, t.image_urls from coms_desktop_info t
    </sql>

    <select id="selectDesktopInfoList" parameterType="DesktopInfo" resultMap="DesktopInfoResult">
        <include refid="selectDesktopInfoVo"/>
        <where>
            <if test="desktopName != null  and desktopName != ''">and t.desktop_name like concat('%', #{desktopName},
                '%')
            </if>
        </where>
        order by t.update_time desc,t.create_time desc
    </select>

    <select id="selectDesktopInfoById" parameterType="Integer" resultMap="DesktopInfoResult">
        <include refid="selectDesktopInfoVo"/>
        where id = #{id}
    </select>

</mapper>