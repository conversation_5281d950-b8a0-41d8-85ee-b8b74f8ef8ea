<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.cpay.task.mapper.ChangeInsTaskMapper">

    <resultMap type="ChangeInsTask" id="ChangeInsTaskResult">
        <result property="id" column="id"/>
        <result property="jobId" column="job_id"/>
        <result property="termSeq" column="term_seq"/>
        <result property="oldDeptId" column="old_dept_id"/>
        <result property="oldGroupId" column="old_group_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="groupId" column="group_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="groupName" column="group_name"/>
        <result property="oldDeptName" column="old_dept_name"/>
        <result property="oldGroupName" column="old_group_name"/>
    </resultMap>

    <sql id="selectChangeInsTaskVo">
        select cut.id,
        cut.job_id,
        cut.term_seq,
        cut.old_dept_id,
        cut.old_group_id,
        cut.dept_id,
        cut.group_id,
        b.dept_name,
        d.name as group_name,
        bb.dept_name as old_dept_name,
        dd.name as old_group_name
        from coms_change_ins_task cut
         LEFT JOIN sys_dept b ON b.dept_id = cut.dept_id
         LEFT JOIN coms_terminal_group d ON d.id = cut.group_id
         LEFT JOIN sys_dept bb ON bb.dept_id = cut.old_dept_id
         LEFT JOIN coms_terminal_group dd ON dd.id = cut.old_group_id
    </sql>

    <select id="selectTaskList" parameterType="ChangeInsTask" resultMap="ChangeInsTaskResult">
        <include refid="selectChangeInsTaskVo"/>
        <where>
            <if test="jobId != null">cut.job_id = #{jobId}</if>
            <if test="termSeq != null and termSeq != ''">and cut.term_seq = #{termSeq}</if>
        </where>
        order by cut.id desc
    </select>


</mapper>