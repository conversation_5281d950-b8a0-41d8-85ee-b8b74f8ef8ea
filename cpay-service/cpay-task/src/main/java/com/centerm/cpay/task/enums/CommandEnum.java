package com.centerm.cpay.task.enums;

import io.swagger.annotations.ApiModel;

/**
 * 指令枚举类
 *
 * <AUTHOR>
 * @date 2019/11/11 14:29
 **/
@ApiModel("心跳下一个命令")
public enum CommandEnum {
    /**
     * 心跳包
     **/
    HEART_0000("0000"),
    /**
     * 开关机信息
     **/
    SWITCH_0001("0001"),
    /**
     * 终端状态
     **/
    STATUS_0002("0002"),
    /**
     * 信息上送
     **/
    UPLOAD_0003("0003"),
    /**
     * 参数下载
     **/
    PARAM_DOWNLOAD_0005("0005"),
    /**
     * 日志上送
     **/
    LOG_UPLOAD_0006("0006"),
    /**
     * 操作指令
     **/
    OPERATE_0013("0013"),

    /**
     * 短消息下发
     **/
    MESSAGE_NOTICE_0014("0014"),
    /**
     * 主题下发
     **/
    DESKTOP_DOWNLOAD_0015("0015"),
    ;


    private String command;

    CommandEnum(String command) {
        this.command = command;
    }

    public String getCommand() {
        return this.command;
    }
}
