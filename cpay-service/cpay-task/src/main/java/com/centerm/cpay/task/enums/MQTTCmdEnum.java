package com.centerm.cpay.task.enums;

/**
 *【MQTT指令】类型
 */
public enum MQTTCmdEnum {

    /**
     *【信息上送】
     **/
    INFO_UPLOAD(0),

    /**
     *【日志抓取】
     **/
    LOG_UPLOAD(1),

    /**
     *【任务更新】
     */
    DOWNLOAD_TASK(2),

    /**
     *【远程指令】
     */
    REMOTE_INSTRUCTION(3),

    /**
     *【广告推送】
     */
    ADVERT_TASK(4),

    /**
     *【参数更新】
     */
    PARAM_UPDATE(5),
    ;

    private final Integer command;

    MQTTCmdEnum(Integer command) {
        this.command = command;
    }

    public Integer getCommand() {
        return this.command;
    }
}