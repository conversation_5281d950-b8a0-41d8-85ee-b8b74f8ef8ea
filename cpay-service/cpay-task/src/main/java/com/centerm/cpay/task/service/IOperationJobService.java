package com.centerm.cpay.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.centerm.cpay.common.domain.task.OperationJob;
import com.centerm.cpay.common.dto.task.DesktopTerminalDto;
import com.centerm.cpay.task.dto.OperationJobAndTaskOutput;

import java.util.List;

/**
 * 终端操作任务 Service接口
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
public interface IOperationJobService extends IService<OperationJob> {

    /**
     * 按条件查询操作任务
     *
     * @param operationJob 操作任务
     * @return : 操作任务信息列表
     * <AUTHOR> Jason
     * @date : 2020/1/10
     */
    List<OperationJobAndTaskOutput> selectOperationJobList(OperationJob operationJob);

    /**
     * 新增修改操作任务
     *
     * @param operationJob 操作任务
     * @return : boolean
     * <AUTHOR> nff
     * @date : 2019/10/22 16:52
     */
    boolean insertOrUpdateOperation(OperationJob operationJob);

    /**
     * 发布任务
     *
     * @param id 任务ID
     * @return : boolean
     * <AUTHOR> nff
     * @date : 2019/10/21 15:13
     */
    Boolean publishJob(Long id);

    /**
     * 发布终端检测任务并发送到QTMS
     *
     * @param operationJob 操作指令
     * @return : boolean
     * <AUTHOR> nff
     * @date : 2019/12/19 14:14
     */
    Boolean publishCheckToQtms(OperationJob operationJob);

    List<DesktopTerminalDto> getDesktopTerminalListByDesktopId(Long desktopId);

    void doCmd(List<String> tusnList, Long jobId);
}
