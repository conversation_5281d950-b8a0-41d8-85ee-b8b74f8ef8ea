package com.centerm.cpay.task.service;

import com.centerm.cpay.task.domain.DownloadJobSub;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 子任务详情 Service接口
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
public interface IDownloadJobSubService extends IService<DownloadJobSub> {

    List<DownloadJobSub> selectDownloadJobSubList(DownloadJobSub downloadJobSub);

    Boolean insertDownloadJobSub(DownloadJobSub downloadJobSub);

    Boolean updateDownloadJobSub(DownloadJobSub downloadJobSub);

    Boolean removeByIds(Long[] idsList);
}
