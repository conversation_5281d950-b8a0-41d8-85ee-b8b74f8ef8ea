package com.centerm.cpay.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.centerm.cpay.task.domain.DesktopJob;

import java.util.List;

/**
 * 定制桌面任务Service接口
 *
 * <AUTHOR>
 * @date 2019-12-17
 */
public interface IDesktopJobService extends IService<DesktopJob> {
    /**
     * 查询定制桌面任务
     *
     * @param id 定制桌面任务ID
     * @return 定制桌面任务
     */
    public DesktopJob selectDesktopJobById(Integer id);

    /**
     * 查询定制桌面任务列表
     *
     * @param desktopJob 定制桌面任务
     * @return 定制桌面任务集合
     */
    public List<DesktopJob> selectDesktopJobList(DesktopJob desktopJob);

}
