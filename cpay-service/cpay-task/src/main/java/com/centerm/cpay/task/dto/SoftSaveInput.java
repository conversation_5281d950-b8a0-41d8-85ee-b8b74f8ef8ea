package com.centerm.cpay.task.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.miser.common.annotation.Excel;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 软件信息保存
 *
 * <AUTHOR>
 * @date 2019/10/09 16:21
 **/
@Data
public class SoftSaveInput {

    @Excel(name = "软件编号")
    @ApiModelProperty(value = "软件编号")
    @NotBlank
    private String code;

    @Excel(name = "软件名称")
    @ApiModelProperty(value = "软件名称")
    @NotBlank
    private String appName;

    @Excel(name = "软件类型")
    @ApiModelProperty(value = "软件类型")
    @NotNull
    private Integer type;


    @Excel(name = "内部版本")
    @ApiModelProperty(value = "内部版本")
    private String versionCode;

    @Excel(name = "内部版本")
    @ApiModelProperty(value = "应用版本")
    private String versionName;

    @Excel(name = "软件大小")
    @ApiModelProperty(value = "软件大小")
    private Integer appSize;

    @Excel(name = "软件路径")
    @ApiModelProperty(value = "软件路径")
    private String appPath;

    @Excel(name = "图标文件")
    @ApiModelProperty(value = "图标文件")
    private String iconPath;

    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    @Excel(name = "文件md5")
    @ApiModelProperty(value = "文件md5")
    private String fileMd5;

    @Excel(name = "应用上传策略", readConverterExp = "0=Cloud Storage,1=Local Storage")
    @ApiModelProperty(value = "应用上传策略")
    private String uploadStrategy;

    @Excel(name = "应用上传状态", readConverterExp = "0=Pending,1=On Going,2=Success,3=Failed")
    @ApiModelProperty(value = "应用上传状态")
    private String uploadStatus;

    @Excel(name = "厂商id")
    @ApiModelProperty(value = "厂商id")
    private Integer firmId;

    @Excel(name = "型号ID")
    @ApiModelProperty(value = "终端型号")
    private String terminalTypes;

    @Excel(name = "机构号")
    @ApiModelProperty(value = "机构id")
    private Long deptId;

    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间", hidden = true)
    private Date createTime;

    private String createBy;
    private String updateBy;

    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间", hidden = true)
    private Date updateTime;
}