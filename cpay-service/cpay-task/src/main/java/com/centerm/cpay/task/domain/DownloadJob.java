package com.centerm.cpay.task.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.miser.common.annotation.Excel;
import org.miser.common.utils.data.DateUtils;

import javax.validation.constraints.Future;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 任务基本信息对象 coms_download_job
 *
 * <AUTHOR>
 * @date 2019-08-27
 */

@ApiModel(value = "下载任务")
@Data
@ToString
@TableName("coms_download_job")
@Accessors(chain = true)
public class DownloadJob {

    @ApiModelProperty(value = "主键id")
    @TableField
    private Long id;

    @Excel(name = "任务名称")
    @ApiModelProperty(value = "任务名称")
    @NotBlank
    private String jobName;

    @Excel(name = "发布类型")
    @ApiModelProperty(value = "发布类型")
    @NotNull
    private Integer releaseType;


    @Excel(name = "发布机构")
    @ApiModelProperty(value = "发布机构")
    private Long deptId;

    @Excel(name = "厂商id")
    @ApiModelProperty(value = "厂商id")
    @NotNull
    private Integer firmId;

    @Excel(name = "发布组别id")
    @ApiModelProperty(value = "发布组别id")
    private Integer groupId;

    @Excel(name = "终端型号id")
    @ApiModelProperty(value = "终端型号id")
    @NotNull
    private String terminalTypes;

    @Excel(name = "终端集合")
    @ApiModelProperty(value = "终端集合(用 `;`连接)")
    private String tusns;

    @Excel(name = "是否显示通知(0:否;1:是)")
    @ApiModelProperty(value = "是否显示通知(0:否;1:是)")
    private Integer showNotify;

    @Excel(name = "是否空闲更新(0:否;1:是)")
    @ApiModelProperty(value = "是否空闲更新(0:否;1:是)")
    private Integer isRealTime;

    @Excel(name = "有效起始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "有效起始时间")
    private Date validStartTime;

    @Excel(name = "有效截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "有效截止时间")
    @Future
    private Date validEndTime;

    @Excel(name = "发布状态")
    @ApiModelProperty(value = "发布状态")
    private Integer status;

    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @Excel(name = "应用所属平台 0:运维平台，1:开放平台")
    @ApiModelProperty(value = "应用所属平台 0:运维平台，1:开放平台")
    private Integer jobCopsSign;

    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @Excel(name = "修改人")
    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @Excel(name = "开发者ID")
    private String devId;

    @Excel(name = "时区")
    @ApiModelProperty(value = "时区")
    private String timeZone;
}


