package com.centerm.cpay.task.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.cpay.common.domain.task.AdvertTask;
import com.centerm.cpay.common.domain.task.ChangeInsTask;
import com.centerm.cpay.task.mapper.AdvertTaskMapper;
import com.centerm.cpay.task.mapper.ChangeInsTaskMapper;
import com.centerm.cpay.task.service.IAdvertTaskService;
import com.centerm.cpay.task.service.IChangeInsTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class AdvertTaskServiceImpl extends ServiceImpl<AdvertTaskMapper, AdvertTask> implements IAdvertTaskService {



    @Override
    public List<AdvertTask> selectTaskList(AdvertTask advertTask) {
        return baseMapper.selectAdvertTaskList(advertTask);
    }

    @Override
    public Integer insert(AdvertTask advertTask) {
        return baseMapper.insert(advertTask);
    }

    @Override
    public int batchInsert(List<AdvertTask> advertJobList) {
        return baseMapper.batchInsert(advertJobList);
    }

    @Override
    public Boolean updateAdvertTaskByJobIdAndTusn(AdvertTask advertTask) {
        return baseMapper.updateAdvertTaskByJobIdAndTusn(advertTask);
    }


}
