package com.centerm.cpay.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.centerm.cpay.common.domain.task.AdvertTask;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface AdvertTaskMapper extends BaseMapper<AdvertTask> {

    List<AdvertTask> selectAdvertTaskList(AdvertTask advertTask);

    Boolean insertAdvertTask(AdvertTask advertTask);


    Boolean updateAdvertTaskByJobIdAndTusn(AdvertTask advertTask);

    Boolean deleteAdvertTaskByIds(@Param("idsList") Long[] idsList);

    int batchInsert(@Param("list") List<AdvertTask> advertJobList);

}