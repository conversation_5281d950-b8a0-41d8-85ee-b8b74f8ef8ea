package com.centerm.cpay.task.dto;

import com.centerm.cpay.task.domain.DownloadJob;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


@ApiModel(value = "下载任务局部参数")
@Data
public class DownloadJobSavePartInput {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "有效起始时间")
    private Date validStartTime;

    @ApiModelProperty(value = "有效截止时间")
    private Date validEndTime;

    @ApiModelProperty(value = "时区")
    private String timeZone;
}
