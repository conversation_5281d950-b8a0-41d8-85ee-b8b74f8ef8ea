package com.centerm.cpay.task.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import org.miser.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 软件查询结果
 *
 * <AUTHOR>
 * @date 2019/10/09 16:26
 **/
@Data
public class SoftQueryResult {

    @Excel(name = "软件类型")
    @ApiModelProperty(value = "软件类型")
    @TableField(exist = false)
    private Integer type;

    @Excel(name = "软件名称")
    @ApiModelProperty(value = "软件名称")
    @TableField(exist = false)
    private String appName;

    @Excel(name = "软件编号")
    @ApiModelProperty(value = "软件编号")
    @TableField(exist = false)
    private String code;

    @Excel(name = "所属机构")
    @ApiModelProperty(value = "所属机构")
    @TableField(exist = false)
    private String SysDeptName;

    @Excel(name = "终端厂商")
    @ApiModelProperty(value = "终端厂商")
    @TableField(exist = false)
    private String firmName;

    private String createBy;
    private String updateBy;
}
