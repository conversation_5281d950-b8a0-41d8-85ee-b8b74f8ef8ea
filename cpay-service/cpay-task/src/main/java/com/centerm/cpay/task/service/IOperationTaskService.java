package com.centerm.cpay.task.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.centerm.cpay.common.domain.task.OperationTask;

/**
 * 终端操作详细 Service接口
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
public interface IOperationTaskService extends IService<OperationTask> {

    List<OperationTask> selectOperationTaskList(OperationTask operationTask);

    Boolean insertOperationTask(OperationTask operationTask);

    Boolean updateOperationTask(OperationTask operationTask);

    Boolean removeByIds(Long[] idsList);

    /**
     * 查询是否有下载任务
     *
     * @param tusn 终端号
     * @return : boolean
     * <AUTHOR> nff
     * @date : 2019/11/28 14:41
     */
    boolean hasDownloadTask(String tusn);

    void resetToCmd(List<OperationTask> list);

    void updateExpiredStatus(Long id);
}
