package com.centerm.cpay.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.centerm.cpay.common.domain.task.DownloadTask;
import com.centerm.cpay.common.dto.task.DownloadTaskDto;

import java.util.List;

/**
 * 终端任务详情 Service接口
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
public interface IDownloadTaskService extends IService<DownloadTask> {

    /**
     * 查询下载任务列表
     *
     * @param downloadTaskDto 查询参数
     * @return : 任务详情
     * <AUTHOR> nff
     * @date : 2019/11/27 17:14
     */
    List<DownloadTaskDto> selectDownloadTaskList(DownloadTaskDto downloadTaskDto);

    /**
     * 查询是否有下载任务
     *
     * @param tusn 终端号
     * @return : boolean
     * <AUTHOR> nff
     * @date : 2019/11/28 14:41
     */
    boolean hasDownloadTask(String tusn);

    /**
     * 查询终端tusn号
     * @param jobId 任务ID
     * @return : list
     * <AUTHOR> nff
     * @date : 2019/12/23 16:22
     */
    List<String> selectTusnsByJobId(Long jobId);

    void resetToCmd(List<Long> idList);
}
