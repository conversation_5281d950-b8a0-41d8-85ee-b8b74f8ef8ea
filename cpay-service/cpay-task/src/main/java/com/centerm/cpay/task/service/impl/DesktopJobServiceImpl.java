package com.centerm.cpay.task.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.cpay.task.domain.DesktopJob;
import com.centerm.cpay.task.mapper.DesktopJobMapper;
import com.centerm.cpay.task.service.IDesktopJobService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 定制桌面任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2019-12-17
 */
@Service
public class DesktopJobServiceImpl extends ServiceImpl<DesktopJobMapper, DesktopJob> implements IDesktopJobService {


    /**
     * 查询定制桌面任务
     *
     * @param id 定制桌面任务ID
     * @return 定制桌面任务
     */
    @Override
    public DesktopJob selectDesktopJobById(Integer id) {
        return baseMapper.selectDesktopJobById(id);
    }

    /**
     * 查询定制桌面任务列表
     *
     * @param desktopJob 定制桌面任务
     * @return 定制桌面任务
     */
    @Override
    public List<DesktopJob> selectDesktopJobList(DesktopJob desktopJob) {
        return baseMapper.selectDesktopJobList(desktopJob);
    }
}
