package com.centerm.cpay.task.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.centerm.cpay.task.domain.TaskJob;
import com.centerm.cpay.task.domain.TaskRecord;
import com.centerm.cpay.task.domain.TaskSubJob;
import com.centerm.cpay.task.dto.TaskInfoDto;
import com.centerm.cpay.task.dto.TaskRecordInput;
import com.centerm.cpay.task.enums.TaskJobStatus;
import com.centerm.cpay.task.service.ITaskJobService;
import com.centerm.cpay.task.service.ITaskRecordService;
import com.centerm.cpay.task.service.ITaskService;
import com.centerm.cpay.task.service.ITaskSubJobService;
import com.centerm.cpay.task.threads.TaskThreadService;
import org.apache.commons.lang3.ArrayUtils;
import org.miser.common.exception.BusinessException;
import org.miser.common.service.IGeneratorService;
import org.miser.common.utils.bean.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @program: cpay
 * @description:
 * @author: Zhang Chong
 * @create: 2019/10/30 15:34
 **/
@Service
public class TaskServiceImpl implements ITaskService {

	@Autowired
	private ITaskJobService jobService;
	@Autowired
	private ITaskSubJobService subJobService;
	@Autowired
	private ITaskRecordService recordService;

	@Autowired
	private IGeneratorService generator;

	@Autowired
	private TaskThreadService taskThreadService;

	@Override
	public boolean save(TaskInfoDto taskInfo) {
		TaskJob taskJob = taskInfo.getTaskJob();
		taskJob.setStatus(TaskJobStatus.TO_PUBLISH);
		// -.修改
		if (taskJob.getId() != null){
			//删除任务详情
			subJobService.remove(Wrappers.<TaskSubJob>lambdaQuery().eq(TaskSubJob::getJobId, taskJob.getId()));
			recordService.remove(Wrappers.<TaskRecord>lambdaQuery().eq(TaskRecord::getJobId, taskJob.getId()));
		}
		// 1.添加任务基础信息
		jobService.saveOrUpdate(taskJob);

		// 2.添加subjob表记录
		List<TaskSubJob> taskSubJobs = taskInfo.getTaskSubJobs();
		taskSubJobs.forEach(taskSubJob -> {
			taskSubJob.setJobId(taskJob.getId());
		});
		subJobService.saveBatch(taskSubJobs);
		return true;
	}

	@Override
	public List<TaskJob> list(TaskJob taskJob) {
		return jobService.list(Wrappers.lambdaQuery(taskJob)
				.select(i -> !"tusns".equals(i.getProperty())));
	}

	@Override
	public TaskInfoDto findSubJob(Long jobId) {
		TaskJob taskJob = jobService.getById(jobId);
		List<TaskSubJob> taskSubJobs = subJobService.list(
				Wrappers.<TaskSubJob>lambdaQuery().eq(TaskSubJob::getJobId, jobId)
		);

		TaskInfoDto result = new TaskInfoDto();
		result.setTaskJob(taskJob);
		result.setTaskSubJobs(taskSubJobs);
		return result;
	}

	@Override
	public List<TaskRecord> findRecord(Long jobId) {
		List<TaskRecord> taskRecords = recordService.list(
				Wrappers.<TaskRecord>lambdaQuery().eq(TaskRecord::getJobId, jobId)
		);
		return taskRecords;
	}

	@Override
	public Boolean publishJob(Long id) {

		// 1.修改任务状态
		TaskJob taskJob = new TaskJob();
		taskJob.setStatus(TaskJobStatus.PUBLISHING);
		taskJob.setId(id);
		if(!jobService.updateById(taskJob)) {
			throw new BusinessException("param.error",id);
		}
		taskJob = jobService.getById(id);

		List<TaskSubJob> taskSubJobs = subJobService.list(Wrappers.<TaskSubJob>lambdaQuery()
				.eq(TaskSubJob::getJobId, id));

		// 2.添加task表（终端任务详情表）记录
		TaskRecordInput taskRecordInput = new TaskRecordInput();
		BeanUtils.copyBeanProp(taskRecordInput, taskJob);

		taskRecordInput.setJobId(taskJob.getId());

		long[] subJobIds = taskSubJobs.stream().mapToLong(TaskSubJob::getId).toArray();
		taskRecordInput.setSubjobId(ArrayUtils.toObject(subJobIds));

		taskThreadService.taskRecordPub(taskRecordInput);

		//3.修改任务状态
		jobService.update(Wrappers.<TaskJob>lambdaUpdate()
				.set(TaskJob::getStatus, TaskJobStatus.PUBLISH_SUCCESS)
				.eq(TaskJob::getId, id));
		return true;
	}

	@Override
	public Boolean removeByIds(List<Long> idList) {
		idList.parallelStream().forEach(id->{
			jobService.removeById(id);
			subJobService.remove(Wrappers.<TaskSubJob>lambdaQuery().eq(TaskSubJob::getJobId, id));
			recordService.remove(Wrappers.<TaskRecord>lambdaQuery().eq(TaskRecord::getJobId, id));
		});
		return true;
	}


}
