package com.centerm.cpay.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.centerm.cpay.common.domain.task.DesktopTask;
import com.centerm.cpay.common.dto.task.DesktopTerminalDto;
import com.centerm.cpay.common.dto.task.TerminalDesktopTaskOutput;

import java.util.List;

/**
 * 桌面定制任务Service接口
 *
 * <AUTHOR>
 * @date 2019-12-17
 */
public interface IDesktopTaskService extends IService<DesktopTask> {
    /**
     * 查询桌面定制任务
     *
     * @param id 桌面定制任务ID
     * @return 桌面定制任务
     */
    public DesktopTask selectDesktopTaskById(Long id);

    /**
     * 查询桌面定制任务列表
     *
     * @param desktopTask 桌面定制任务
     * @return 桌面定制任务集合
     */
    public List<DesktopTask> selectDesktopTaskList(DesktopTask desktopTask);

    /**
     * 判断终端是否有主题下载额任务
     *
     * @param tusn
     * @return
     */
    boolean hasDesktopTask(String tusn);


    /**
     * 判断终端是否有主题下载额任务
     *
     * @param tusn
     * @return
     */
    TerminalDesktopTaskOutput selectDesktopTask(String tusn);

    /**
     * 根据主题id查询定制桌面终端信息列表
     *
     * @param desktopId
     * @return
     */
    List<DesktopTerminalDto> getDesktopTerminalListByDesktopId(Long desktopId);
}
