package com.centerm.cpay.task.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import org.miser.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 终端下载限制对象 coms_download_limit
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
@ApiModel(value = "${tableComment}")
@Data
@ToString
@TableName("coms_download_limit")
@Accessors(chain = true)
public class DownloadLimit {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @Excel(name = "终端序列号")
    @ApiModelProperty(value = "终端序列号")
    private String tusn;

    @Excel(name = "下载开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "下载开始时间")
    private Date beginTime;

    @Excel(name = "下载资源数")
    @ApiModelProperty(value = "下载资源数")
    private Long resourceNum;

    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}


