package com.centerm.cpay.task.threads;

import com.centerm.cpay.task.constants.JobConstants;
import com.centerm.cpay.task.domain.TaskRecord;
import com.centerm.cpay.task.dto.TaskRecordInput;
import com.centerm.cpay.task.enums.TaskDownloadStatus;
import com.centerm.cpay.task.enums.TaskReleaseType;
import com.centerm.cpay.task.service.ITaskRecordService;
import com.centerm.cpay.common.domain.terminal.TerminalInfo;
import com.centerm.cpay.terminal.service.ITerminalInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.miser.common.utils.bean.BeanUtils;
import org.miser.common.utils.data.ApiAssert;
import org.miser.common.utils.data.CtUtils;
import org.miser.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @program: cpay
 * @description: 批量操作线程
 * @author: Zhang Chong
 * @create: 2019/10/31 11:03
 **/
@Slf4j
@Component
@Lazy
public class TaskThreadService {

    @Autowired
    private ITerminalInfoService terminalInfoService;
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private ITaskRecordService taskRecordService;

    /**
     * TODO 以线程的方式批量添加任务记录
     *
     * @param input
     * @return
     */
    public void taskRecordPub(TaskRecordInput input) {

        log.info("任务编号：{}, 正在进行新增操作...", input.getJobId());
        /*
         * 1.解析出终端列表
         */
        List<String> tusnList = getTusnList(input);

        /*
         * 2.根据系统参数得到分块的终端列表
         */
        //单次批量最大值
        Integer MAX_LENGTH = Integer.valueOf(sysConfigService.selectConfigByKey(JobConstants.BATCH_DATA_MAX_LENGTH));
        //终端列表个数
        int tusnSize = tusnList.size();
        //分块个数
        int limitSize = (tusnSize + MAX_LENGTH - 1) / MAX_LENGTH;
        //按系统参数得到的分块终端列表
        List<List<String>> tusnLists = Stream.iterate(0, f -> f + 1).limit(limitSize)
                .map(a -> tusnList.stream()
                        .skip(a * MAX_LENGTH)
                        .limit(MAX_LENGTH).parallel()
                        .collect(Collectors.toList())
                ).collect(Collectors.toList());
        /*
         * 3.遍历分块终端列表进行数据存储
         */
        tusnLists.parallelStream().forEach(a -> {
            //遍历子任务数组、每个分块终端列表均存储一次子任务
            Arrays.stream(input.getSubjobId()).parallel()
                    .forEach(subjobId -> {
                        TaskRecord record = new TaskRecord();
                        BeanUtils.copyBeanProp(record, input);
                        record.setStatus(TaskDownloadStatus.TO_RELEASE);
                        record.setSubjobId(subjobId);
                        taskRecordSaveBatch(record, a);
                        log.info("\n终端个数：{},最大插入数：{},分批插入数：{}, 子任务编号：{}", tusnSize, MAX_LENGTH, a.size(), subjobId);
                    });
        });
    }

    /**
     * 获取终端列表（按条件或者按机构方式）
     *
     * @param input
     * @return
     */
    private List<String> getTusnList(TaskRecordInput input) {
        List<String> tusnList = null;
        //按机构发布
        if (TaskReleaseType.DEPT_GROUP == input.getReleaseType()) {
            //TODO 按机构、分组、厂商型号找出对应终端
            TerminalInfo terminalInfo = new TerminalInfo();
            terminalInfo.setDeptId(input.getDeptId());
            terminalInfo.setTerminalGroupId(input.getGroupId().longValue());
            tusnList = terminalInfoService.selectTusnByInsGroup(null);
        } else {
            if (StringUtils.isNotEmpty(input.getTusns())) {
                tusnList = Arrays.asList(input.getTusns().split(",|;"));
            }
        }
        ApiAssert.isTrue("task.terminal.null", !CtUtils.isEmpty(tusnList));
        return tusnList;
    }

    /**
     * 按一个终端一条记录的方式 批量保存 task任务
     *
     * @param record   任务相关信息
     * @param tusnList 待保存的终端列表
     * @return
     */
    private boolean taskRecordSaveBatch(TaskRecord record, List<String> tusnList) {
        List<TaskRecord> taskRecords = tusnList.parallelStream()
                .map(tusn -> {
                    TaskRecord taskRecord = new TaskRecord();
                    BeanUtils.copyBeanProp(taskRecord, record);
                    taskRecord.setTusn(tusn);
                    return taskRecord;
                })
                .collect(Collectors.toList());
        return taskRecordService.saveBatch(taskRecords);
    }
}
