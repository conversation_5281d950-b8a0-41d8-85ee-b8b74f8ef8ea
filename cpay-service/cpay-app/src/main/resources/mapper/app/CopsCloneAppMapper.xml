<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.cpay.app.mapper.CopsCloneAppMapper">

    <resultMap type="CopsCloneApp" id="CopsCloneAppResult">
        <result property="id" column="id"/>
        <result property="devId" column="dev_id"/>
        <result property="apkId" column="apk_id"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectCopsCloneAppVo">
        select t.id, t.dev_id, t.apk_id, t.create_by, t.update_by, t.create_time, t.update_time from cops_clone_app t
    </sql>

    <select id="selectCopsCloneAppList" parameterType="CopsCloneApp" resultMap="CopsCloneAppResult">
        <include refid="selectCopsCloneAppVo"/>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="apkId != null">and apk_id = #{apkId}</if>
            <if test="devId != null and devId != ''">and dev_id = #{devId}</if>
        </where>
    </select>

    <select id="selectCopsCloneAppById" parameterType="Long" resultMap="CopsCloneAppResult">
        <include refid="selectCopsCloneAppVo"/>
        where id = #{id}
    </select>

    <delete id="removeCopsCloneApp">
        delete from cops_clone_app
        <where>
            <if test="apkId != null">and apk_id = #{apkId}</if>
            <if test="devId != null and devId != ''">and dev_id = #{devId}</if>
        </where>
    </delete>

</mapper>