<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.cpay.app.mapper.AppTypeMapper">

    <resultMap type="AppType" id="AppTypeResult">
        <result property="id" column="id"/>
        <result property="typeCode" column="type_code"/>
        <result property="typeName" column="type_name"/>
        <result property="iconPath" column="icon_path"/>
        <result property="deptId" column="dept_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAppTypeVo">
        select id, type_code, type_name, icon_path, dept_id, create_time, update_time from coms_app_type
    </sql>

    <select id="selectAppTypeList" parameterType="AppType" resultMap="AppTypeResult">
        <include refid="selectAppTypeVo"/>
        <where>
            <if test="typeCode!=null and typeCode!=''">
                and type_code = #{typeCode}
            </if>
            <if test="typeName!=null and typeName!=''">
                and type_name = #{typeName}
            </if>
            <if test="deptId!=null">
                and dept_id = #{deptId}
            </if>
        </where>
        order by create_time desc,update_time desc
    </select>

</mapper>