<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.cpay.app.mapper.AppFileMapper">

    <resultMap type="AppFile" id="AppFileResult">
        <result property="id" column="id"/>
        <result property="fileSavePath" column="file_save_path"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileMd5" column="file_md5"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAppFileVo">
        select id, file_save_path, file_size, file_md5, create_time, update_time from coms_app_file
    </sql>

    <select id="selectAppFileList" parameterType="AppFile" resultMap="AppFileResult">
        <include refid="selectAppFileVo"/>
        <where>
        </where>
    </select>

</mapper>