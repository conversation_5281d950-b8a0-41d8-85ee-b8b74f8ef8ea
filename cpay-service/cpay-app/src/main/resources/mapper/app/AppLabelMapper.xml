<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.cpay.app.mapper.AppLabelMapper">

    <resultMap type="AppLabel" id="AppLabelResult">
        <result property="id" column="id"/>
        <result property="labelName" column="label_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAppLabelVo">
        select id,
        label_name,
        dept_id,
        create_by,
        create_time,
        update_by,
        update_time
        from coms_app_label
    </sql>

    <!--  查询二级机构树所有机构id  -->
    <sql id="deptSecTreeDeptIdSQL">
        SELECT
        dept_id
        FROM
        sys_dept
        WHERE
        ancestors LIKE concat( #{secAncestors}, ',', '%' )
        OR ancestors = #{secAncestors}
        OR dept_id = substring_index( #{secAncestors}, ",", - 1 )
    </sql>

    <select id="selectAppLabelList" resultMap="AppLabelResult">
        <include refid="selectAppLabelVo"/>
        <where>
            dept_id IN (
            <include refid="deptSecTreeDeptIdSQL"/>
            )
            <if test="appLabel.labelName != null and appLabel.labelName != ''">
                and label_name like CONCAT('%',#{appLabel.labelName},'%')
            </if>
        </where>
        order by create_time desc,update_time desc
    </select>

    <select id="selectDeptNotLabelList" resultMap="AppLabelResult">
        SELECT
        d.id,
        d.label_name,
        d.dept_id,
        d.create_by,
        d.create_time,
        d.update_by,
        d.update_time
        FROM
        (
        SELECT
        a.id,
        a.label_name,
        a.dept_id,
        t.dept_id as use_dept,
        a.create_by,
        a.create_time,
        a.update_by,
        a.update_time
        FROM
        (
        <include refid="selectAppLabelVo"/>
        <where>
            dept_id IN (
            <include refid="deptSecTreeDeptIdSQL"/>
            )
        </where>
        ) a
        LEFT JOIN
        coms_dept_label t ON (a.id = t.label_id and t.dept_id = #{deptId})
        <if test="labelName != null and labelName != ''">
            AND a.label_name LIKE CONCAT('%',#{labelName},'%')
        </if>
        ) as d
        where d.use_dept IS NULL
    </select>


    <select id="ownAndPermissionLabelList" resultMap="AppLabelResult">
        <include refid="selectAppLabelVo"/>
        <where>
            <if test="deptId != null and deptId != ''">
                and dept_id like concat('%', #{deptId},'%')
            </if>
            <if test="permission == 1">
                or label_type = #{permission}
            </if>
            <if test="labelNameList != null">
                or ( label_name in
                <foreach collection="labelNameList" open="(" close=")" item="labelNames" separator=",">
                    #{labelNames}
                </foreach>
                )
            </if>
        </where>
    </select>

    <select id="selectAppLabelById" parameterType="Long" resultMap="AppLabelResult">
        <include refid="selectAppLabelVo"/>
        where id = #{id}
    </select>

    <select id="getDeptSecAncestorsById" parameterType="Long" resultType="string">
        SELECT
        IF(
        LENGTH( substring_index( ancestors, ",", 2 ) ) = LENGTH( substring_index( ancestors, ",", 3 ) ),
        concat( substring_index( ancestors, ",", 2 ), ',', #{deptId} ),
        substring_index( ancestors, ",", 3 )
        ) AS ancestors
        FROM
        sys_dept
        WHERE
        dept_id = #{deptId}
    </select>

</mapper>