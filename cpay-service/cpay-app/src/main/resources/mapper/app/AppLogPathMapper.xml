<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.centerm.cpay.app.mapper.AppLogPathMapper">

    <resultMap type="AppLogPath" id="AppLogPathResult">
        <result property="id" column="id"/>
        <result property="appCode" column="app_code"/>
        <result property="appName" column="app_name"/>
        <result property="logPath" column="log_path"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAppLogPathVo">
        select id, app_code, app_name, log_path, create_time, update_time from coms_app_log_path
    </sql>

    <select id="selectAppLogPathList" parameterType="AppLogPath" resultMap="AppLogPathResult">
        <include refid="selectAppLogPathVo"/>
        <where>
        </where>
    </select>

</mapper>