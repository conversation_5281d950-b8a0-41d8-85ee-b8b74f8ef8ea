package com.centerm.cpay.app.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import org.miser.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 应用日志路径对象 coms_app_log_path
 *
 * <AUTHOR>
 * @date 2019-08-26
 */
@ApiModel(value = "${tableComment}")
@Data
@ToString
@TableName("coms_app_log_path")
@Accessors(chain = true)
public class AppLogPath {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @Excel(name = "软件编码")
    @ApiModelProperty(value = "软件编码")
    private String appCode;

    @Excel(name = "软件名称")
    @ApiModelProperty(value = "软件名称")
    private String appName;

    @Excel(name = "日志路径")
    @ApiModelProperty(value = "日志路径")
    private String logPath;


}


