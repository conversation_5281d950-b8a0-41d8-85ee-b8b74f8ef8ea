package com.centerm.cpay.app.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.centerm.cpay.common.dto.base.CommonEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.miser.common.annotation.Excel;

import javax.validation.constraints.NotBlank;

/**
 * 标签信息对象 coms_app_label
 *
 * <AUTHOR>
 * @date 2019-09-23
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "标签信息")
@Data
@ToString
@TableName("coms_app_label")
@Accessors(chain = true)
public class AppLabel extends CommonEntity {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @Excel(name = "标签名称")
    @ApiModelProperty(value = "标签名称")
    @NotBlank
    private String labelName;


}


