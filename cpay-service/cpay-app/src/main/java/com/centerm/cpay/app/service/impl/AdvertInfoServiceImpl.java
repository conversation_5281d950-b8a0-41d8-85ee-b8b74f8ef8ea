package com.centerm.cpay.app.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.cpay.common.constant.app.AdvertConstants;
import com.centerm.cpay.common.domain.app.AdvertInfo;
import com.centerm.cpay.common.dto.app.AdvertAuditInput;
import com.centerm.cpay.common.dto.app.AdvertInfoInOutput;
import com.centerm.cpay.common.dto.app.AdvertInfoTmsInOutput;
import com.centerm.cpay.common.dto.app.AdvertIsDisabled;
import com.centerm.cpay.app.mapper.AdvertInfoMapper;
import com.centerm.cpay.app.service.IAdvertInfoService;
import lombok.extern.slf4j.Slf4j;
import org.miser.common.core.support.Convert;
import org.miser.common.utils.data.ApiAssert;
import org.miser.common.utils.data.CtUtils;
import org.miser.common.utils.data.StringUtils;
import org.miser.file.service.IFileService;
import org.miser.system.domain.SysDept;
import org.miser.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 广告信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2019-09-25
 */
@Slf4j
@Service
public class AdvertInfoServiceImpl extends ServiceImpl<AdvertInfoMapper, AdvertInfo> implements IAdvertInfoService {

    @Autowired
    private IFileService fileService;
    @Autowired
    private ISysDeptService deptService;

    /**
     * 分隔符
     **/
    public static final String DEPT_SPLIT = ",";

    @Override
    public List<AdvertInfoInOutput> selectAdvertInfoList(AdvertInfoInOutput advertInfoInOutput) {
        List<AdvertInfoInOutput> advertList = baseMapper.selectAdvertInfoList(advertInfoInOutput);
        if (CtUtils.isNotEmpty(advertList)) {
            advertList.forEach(item -> {
                item.setPicPath(fileService.getFileFullPath(item.getPicPath()));
            });
        }
        return advertList;
    }

    @Override
    public List<AdvertInfoInOutput> selectTmsAdvertInfoList(AdvertInfoTmsInOutput advertInfoInOutput) {
        //有组别的情况，按分组查询（本级加上级机构的）+ 所有本级机构及以上机构没分组的广告
        // 没组别的时候，按照机构及以上机构没分组的广告
        //获取本级+上级机构
        SysDept sysDept = deptService.selectDeptById(advertInfoInOutput.getDeptId());
        if (CtUtils.isEmpty(sysDept)) {
            ApiAssert.failure("institution.not.exist", "deptId");
        }
        String[] parentsDeptIds = (advertInfoInOutput.getDeptId() + DEPT_SPLIT + sysDept.getAncestors()).split(DEPT_SPLIT);
        advertInfoInOutput.setParentsDeptIds(parentsDeptIds);
        List<AdvertInfoInOutput> advertList = baseMapper.selectTmsAdvertInfoListByGroup(advertInfoInOutput);
        if (CtUtils.isNotEmpty(advertList)) {
            advertList.forEach(item -> {
                item.setPicPath(fileService.getFileFullPath(item.getPicPath()));
            });
        }
        return advertList;
    }

    @Override
    public Boolean deleteAdvertInfo(String ids) {
        for (Long id : Convert.toLongList(ids)) {
            AdvertInfo advertInfo = getById(id);
            try {
                //删除文件
                fileService.deleteFile(advertInfo.getAdPath());
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        removeByIds(Convert.toLongList(ids));
        return true;
    }

    @Override
    public Boolean auditAdvertInfo(AdvertAuditInput advertAuditInput) {
        AdvertInfo paraAdvertInfo = new AdvertInfo();
        if (advertAuditInput.getIsPass()) {
            paraAdvertInfo.setStatus(AdvertConstants.STATUS_REVIEW_PASS);
            paraAdvertInfo.setReviewMsg(StringUtils.isEmpty(advertAuditInput.getReviewMsg()) ? AdvertConstants.MSG_REVIEW_PASS : advertAuditInput.getReviewMsg());
            paraAdvertInfo.setIsDisabled(AdvertConstants.ADVERT_ABLE);
        } else {
            paraAdvertInfo.setStatus(AdvertConstants.STATUS_REVIEW_REFUSE);
            paraAdvertInfo.setReviewMsg(StringUtils.isEmpty(advertAuditInput.getReviewMsg()) ? AdvertConstants.MSG_REVIEW_NOTPASS : advertAuditInput.getReviewMsg());
            paraAdvertInfo.setIsDisabled(AdvertConstants.ADVERT_DISABLED);
        }
        paraAdvertInfo.setReviewUser(advertAuditInput.getReviewUser());
        paraAdvertInfo.setReviewTime(new Date());
        paraAdvertInfo.setId(advertAuditInput.getId());
        return updateById(paraAdvertInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateIsDisabled(AdvertIsDisabled advertIsDisabled) {
        AdvertInfo paraAdvertInfo = new AdvertInfo();
        if (advertIsDisabled.getIsDisabled()) {
            paraAdvertInfo.setIsDisabled(AdvertConstants.ADVERT_DISABLED);
        } else {
            paraAdvertInfo.setIsDisabled(AdvertConstants.ADVERT_ABLE);
        }
        paraAdvertInfo.setId(advertIsDisabled.getId());
        return updateById(paraAdvertInfo);
    }

    @Override
    public Boolean saveOrUpdateAdvert(AdvertInfo advertInfo) {
        if (CtUtils.isEmpty(advertInfo.getId())) {//添加
            if (CtUtils.isNotEmpty(advertInfo.getPicPath())) {
                fileService.moveTmpFile(advertInfo.getPicPath());
            }
        } else {//修改,广告图片不为空
            AdvertInfoInOutput advertInfoById = getAdvertInfoById(advertInfo.getId());
            if (CtUtils.isNotEmpty(advertInfo.getPicPath()) && !advertInfo.getPicPath().equals(advertInfoById.getPicPath())) {
                fileService.moveTmpFile(advertInfo.getPicPath());
            }
        }
        if (CtUtils.isEmpty(advertInfo.getGroupId())) {
            advertInfo.setGroupId(0l);
        }
        return saveOrUpdate(advertInfo);
    }


    @Override
    public AdvertInfoInOutput getAdvertInfoById(Long id) {
        AdvertInfoInOutput advertInfo = baseMapper.getAdverttById(id);
        if (advertInfo != null) {
            if (StringUtils.isNotEmpty(advertInfo.getPicPath())) {
                advertInfo.setPicPath(fileService.getFileFullPath(advertInfo.getPicPath()));
            }
        }
        return advertInfo;
    }
}