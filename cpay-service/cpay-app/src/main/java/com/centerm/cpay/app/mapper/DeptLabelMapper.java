package com.centerm.cpay.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.centerm.cpay.app.domain.DeptLabel;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 机构标签关联Mapper接口
 *
 * <AUTHOR>
 * @date 2020-03-03
 */
@Repository
public interface DeptLabelMapper extends BaseMapper<DeptLabel> {
    /**
     * 查询机构标签关联
     *
     * @param deptId 机构标签关联ID
     * @return 机构标签关联
     */
    //public DeptLabel selectDeptLabelById(Long deptId);

    /**
     * 查询机构标签关联列表
     *
     * @param deptLabel 机构标签关联
     * @return 机构标签关联集合
     */
    public List<DeptLabel> selectDeptLabelList(DeptLabel deptLabel);


    Boolean removeByDeptLabelIds(@Param("labelIdsList") List<Long> labelIdsList, @Param("deptId") Long deptId);
}
