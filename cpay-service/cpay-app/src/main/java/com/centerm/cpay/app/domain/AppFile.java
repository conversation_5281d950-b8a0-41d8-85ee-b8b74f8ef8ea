package com.centerm.cpay.app.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import org.miser.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 应用文件路径对象 coms_app_file
 *
 * <AUTHOR>
 * @date 2019-08-26
 */
@ApiModel(value = "${tableComment}")
@Data
@ToString
@TableName("coms_app_file")
@Accessors(chain = true)
public class AppFile {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @Excel(name = "文件路径")
    @ApiModelProperty(value = "文件路径")
    private String fileSavePath;

    @Excel(name = "文件大小")
    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    @Excel(name = "文件md5")
    @ApiModelProperty(value = "文件md5")
    private String fileMd5;


}


