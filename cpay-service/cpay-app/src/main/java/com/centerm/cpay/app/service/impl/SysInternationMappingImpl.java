package com.centerm.cpay.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.cpay.common.domain.system.SysInternationMapping;
import com.centerm.cpay.app.mapper.SysInternationMappingMapper;
import com.centerm.cpay.app.service.ISysInternationMappingService;
import com.centerm.cpay.common.enums.sys.LanguageTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-02-03
 **/
@Service
public class SysInternationMappingImpl extends ServiceImpl<SysInternationMappingMapper, SysInternationMapping> implements ISysInternationMappingService {

    private final String split = "_";

    @Override
    public List<SysInternationMapping> getInternationInfo(Integer type) {
        LambdaQueryWrapper<SysInternationMapping> queryWrapper = new LambdaQueryWrapper<SysInternationMapping>()
                .select(SysInternationMapping::getCode, SysInternationMapping::getLName, SysInternationMapping::getLType, SysInternationMapping::getMappingType)
                .eq(SysInternationMapping::getMappingType, type);
        return this.list(queryWrapper);
    }

    @Override
    public Map<String, String> getInternationInfoByCode(String langCountry, String code) {
        Locale chinese = Locale.SIMPLIFIED_CHINESE;
        if (langCountry.equals(chinese.getLanguage() + split + chinese.getCountry())) {
            LambdaQueryWrapper<SysInternationMapping> queryWrapper = new LambdaQueryWrapper<SysInternationMapping>()
                    .select(SysInternationMapping::getCode, SysInternationMapping::getLName)
                    .eq(SysInternationMapping::getGroupKey, code).eq(SysInternationMapping::getLType, LanguageTypeEnum.zh_CN.getCode());
            List<SysInternationMapping> list = this.list(queryWrapper);
            return CollectionUtils.isEmpty(list) ? null : list.stream()
                    .collect(Collectors.toMap(SysInternationMapping::getCode, SysInternationMapping::getLName));
        }
        Locale vietnamese = new Locale("vi", "VN");
        if (langCountry.equals(vietnamese.getLanguage() + split + vietnamese.getCountry())) {
            LambdaQueryWrapper<SysInternationMapping> queryWrapper = new LambdaQueryWrapper<SysInternationMapping>()
                    .select(SysInternationMapping::getCode, SysInternationMapping::getLName)
                    .eq(SysInternationMapping::getGroupKey, code).eq(SysInternationMapping::getLType, LanguageTypeEnum.vi_VN.getCode());
            List<SysInternationMapping> list = this.list(queryWrapper);
            return CollectionUtils.isEmpty(list) ? null : list.stream()
                    .collect(Collectors.toMap(SysInternationMapping::getCode, SysInternationMapping::getLName));
        }

        LambdaQueryWrapper<SysInternationMapping> queryWrapper = new LambdaQueryWrapper<SysInternationMapping>()
                .select(SysInternationMapping::getCode, SysInternationMapping::getLName)
                .eq(SysInternationMapping::getGroupKey, code).eq(SysInternationMapping::getLType, LanguageTypeEnum.en_US.getCode());
        List<SysInternationMapping> list = this.list(queryWrapper);
        return CollectionUtils.isEmpty(list) ? null : list.stream()
                .collect(Collectors.toMap(SysInternationMapping::getCode, SysInternationMapping::getLName));
    }

    @Override
    public String getNameByCode(String langCountry, String code) {

        Locale us = Locale.US;
        Locale chinese = Locale.SIMPLIFIED_CHINESE;
        Locale vietnamese = new Locale("vi", "VN");
        if (langCountry.equals(chinese.getLanguage() + split + chinese.getCountry())) {
            LambdaQueryWrapper<SysInternationMapping> queryWrapper = new LambdaQueryWrapper<SysInternationMapping>()
                    .select(SysInternationMapping::getCode, SysInternationMapping::getLName)
                    .eq(SysInternationMapping::getCode, code).eq(SysInternationMapping::getLType, LanguageTypeEnum.zh_CN.getCode());
            return this.getOne(queryWrapper).getLName();
        } else if (langCountry.equals(vietnamese.getLanguage() + split + vietnamese.getCountry())) {
            LambdaQueryWrapper<SysInternationMapping> queryWrapper = new LambdaQueryWrapper<SysInternationMapping>()
                    .select(SysInternationMapping::getCode, SysInternationMapping::getLName)
                    .eq(SysInternationMapping::getCode, code).eq(SysInternationMapping::getLType, LanguageTypeEnum.vi_VN.getCode());
            return this.getOne(queryWrapper).getLName();
        } else {
            LambdaQueryWrapper<SysInternationMapping> queryWrapper = new LambdaQueryWrapper<SysInternationMapping>()
                    .select(SysInternationMapping::getCode, SysInternationMapping::getLName)
                    .eq(SysInternationMapping::getCode, code).eq(SysInternationMapping::getLType, LanguageTypeEnum.en_US.getCode());
            return this.getOne(queryWrapper).getLName();
        }
    }
}
