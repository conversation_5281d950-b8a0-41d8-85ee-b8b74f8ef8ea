package com.centerm.cpay.app.service.impl;

import java.util.Date;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.centerm.cpay.common.domain.app.AppType;
import com.centerm.cpay.app.mapper.AppTypeMapper;
import com.centerm.cpay.app.service.IAppTypeService;
import org.miser.common.core.service.BaseServiceImpl;
import org.miser.common.utils.data.CtUtils;
import org.miser.file.service.IFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 应用类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2019-08-26
 */
@Service
public class AppTypeServiceImpl extends BaseServiceImpl<AppTypeMapper, AppType> implements IAppTypeService {

    @Autowired
    private IFileService fileService;

    @Override
    public List<AppType> selectAppTypeList(AppType appType) {
        List<AppType> appTypeList = baseMapper.selectAppTypeList(appType);
        if (CtUtils.isNotEmpty(appTypeList)) {
            appTypeList.forEach(item -> item.setIconPath(fileService.getFileFullPath(item.getIconPath())));
        }
        return appTypeList;
    }

    @Override
    public Boolean test() {
        AppType appType = new AppType();
        appType.setId(0);
        appType.setTypeCode("");
        appType.setTypeName("");
        appType.setIconPath("");
        appType.setDeptName("");
        appType.setDeptCode("");
        appType.setDeptId(0L);
        appType.setUserId(0L);
        appType.setCreateTime(new Date());
        appType.setCreateBy("");
        appType.setUpdateBy("");
        appType.setUpdateTime(new Date());
        List<AppType> appType1 = list(new QueryWrapper<>(appType));
        return save(appType);
    }
}