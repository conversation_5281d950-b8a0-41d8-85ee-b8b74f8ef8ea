package com.centerm.cpay.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.centerm.cpay.app.domain.CopsDevPermission;

import java.util.List;

/**
 * 开发者权限Service接口
 *
 * <AUTHOR>
 * @date 2020-02-18
 */
public interface ICopsDevPermissionService extends IService<CopsDevPermission> {
    /**
     * 查询开发者权限
     *
     * @param id 开发者权限ID
     * @return 开发者权限
     */
    public CopsDevPermission selectCopsDevPermissionById(Long id);

    /**
     * 查询开发者权限列表
     *
     * @param copsDevPermission 开发者权限
     * @return 开发者权限集合
     */
    public List<CopsDevPermission> selectCopsDevPermissionList(CopsDevPermission copsDevPermission);

}
