package com.centerm.cpay.app.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.cpay.app.domain.AppTemporary;
import com.centerm.cpay.app.mapper.AppTemporaryMapper;
import com.centerm.cpay.app.service.IAppTemporaryService;
import org.miser.common.utils.data.ApiAssert;
import org.miser.common.utils.data.StringUtils;

import org.miser.system.domain.SysUser;
import org.miser.system.service.ISysConfigService;
import com.centerm.yunos.signapk.ApkSignatureUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 应用信息临时Service业务层处理
 *
 * <AUTHOR>
 * @date 2019-08-26
 */
@Slf4j
@Service
public class AppTemporaryServiceImpl extends ServiceImpl<AppTemporaryMapper, AppTemporary> implements IAppTemporaryService {



}
