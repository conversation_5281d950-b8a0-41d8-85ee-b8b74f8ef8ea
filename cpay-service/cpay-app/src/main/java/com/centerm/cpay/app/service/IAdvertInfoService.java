package com.centerm.cpay.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.centerm.cpay.common.domain.app.AdvertInfo;
import com.centerm.cpay.common.dto.app.AdvertAuditInput;
import com.centerm.cpay.common.dto.app.AdvertInfoInOutput;
import com.centerm.cpay.common.dto.app.AdvertInfoTmsInOutput;
import com.centerm.cpay.common.dto.app.AdvertIsDisabled;

import java.util.List;

/**
 * 广告信息 Service接口
 *
 * <AUTHOR>
 * @date 2019-09-25
 */
public interface IAdvertInfoService extends IService<AdvertInfo> {

    /**
     * 根据条件分页查询广告信息列表
     *
     * @param advertInfoInOutput 广告信息
     * @return 广告信息集合信息
     */
    List<AdvertInfoInOutput> selectAdvertInfoList(AdvertInfoInOutput advertInfoInOutput);

    /**
     * 根据条件查询TMS广告信息列表
     * 终端广告信息获取
     * @param advertInfoInOutput 广告信息
     * @return 广告信息集合信息
     */
    List<AdvertInfoInOutput> selectTmsAdvertInfoList(AdvertInfoTmsInOutput advertInfoInOutput);

    /**
     * 删除广告信息
     *
     * @param ids 广告信息
     * @return : boolean
     * <AUTHOR> Jason
     * @date : 2019/10/17
     */
    Boolean deleteAdvertInfo(String ids);

    /**
     * 审核广告
     *
     * @param advertAuditInput 审核信息
     * @return : boolean
     * <AUTHOR> Jason
     * @date : 2019/10/28
     */
    Boolean auditAdvertInfo(AdvertAuditInput advertAuditInput);

    /**
     * 广告是否停用
     *
     * @param advertIsDisabled 启用/停用信息
     * @return : boolean
     * <AUTHOR> Jason
     * @date : 2019/10/28
     */
    Boolean updateIsDisabled(AdvertIsDisabled advertIsDisabled);

    /**
     * 根据id查询广告信息列表
     *
     * @param id 广告id
     * @return 广告信息
     */
    AdvertInfoInOutput getAdvertInfoById(Long id);

    Boolean saveOrUpdateAdvert(AdvertInfo advertInfo);

}
