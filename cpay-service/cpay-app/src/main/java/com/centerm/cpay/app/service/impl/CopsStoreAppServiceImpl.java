package com.centerm.cpay.app.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.cpay.common.dto.app.StoreAppInfoDto;
import com.centerm.cpay.common.utils.CommonUtils;
import com.centerm.cpay.common.domain.app.CopsStoreApp;
import com.centerm.cpay.common.dto.app.UserAndCloneAppInOutput;
import com.centerm.cpay.app.mapper.CopsStoreAppMapper;
import com.centerm.cpay.app.service.ICopsStoreAppService;
import org.miser.file.service.IFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 开放平台-市场应用Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-01-19
 */
@Service
public class CopsStoreAppServiceImpl extends ServiceImpl<CopsStoreAppMapper, CopsStoreApp> implements ICopsStoreAppService {

    @Autowired
    private IFileService fileService;

    /**
     * 查询开放平台-市场应用
     *
     * @param id 开放平台-市场应用ID
     * @return 开放平台-市场应用
     */
    @Override
    public CopsStoreApp selectCopsStoreAppById(Long id) {
        return baseMapper.selectCopsStoreAppById(id);
    }

    /**
     * 查询开放平台-市场应用列表
     *
     * @param copsStoreApp 开放平台-市场应用
     * @return 开放平台-市场应用
     */
    @Override
    public List<CopsStoreApp> selectCopsStoreAppList(CopsStoreApp copsStoreApp) {
        return baseMapper.selectCopsStoreAppList(copsStoreApp);
    }

    /**
     * 查询克隆应用列表
     *
     * @param storeAppInfoDto 开放平台应用市场应用信息
     * @return 开放平台克隆应用列表
     */
    @Override
    public List<StoreAppInfoDto> getCloneAppList(StoreAppInfoDto storeAppInfoDto) {
        return baseMapper.getCloneAppList(storeAppInfoDto);
    }

    /**
     * 删除开发者克隆信息
     *
     * @param ids   定制banner信息ids
     * @param devId 开发者id
     * @return 结果
     */
    @Override
    public Boolean removeCopsCloneApp(Long[] ids, String devId) {
        for (Long appId : ids) {
            baseMapper.removeCopsCloneApp(appId, devId);
        }
        return true;
    }

    /**
     * 查询应用市场中我的应用和通用应用
     *
     * @param userAndCloneAppInOutput
     * @return 应用市场中我的应用和通用应用
     */
    @Override
    public List<UserAndCloneAppInOutput> getUserAppAndCloneAppList(UserAndCloneAppInOutput userAndCloneAppInOutput) {
        List<UserAndCloneAppInOutput> list = baseMapper.getUserAppAndCloneAppList(userAndCloneAppInOutput);
        for (UserAndCloneAppInOutput appInfo:list) {
            appInfo.setApkPath(fileService.getFileFullPath(appInfo.getApkPath()));
            appInfo.setIconPath(fileService.getFileFullPath(appInfo.getIconPath()));
            appInfo.setPicPaths(CommonUtils.appendSaveUrl(appInfo.getPicPaths()));
        }
        return list;
    }
}
