package com.centerm.cpay.app.mapper;

import com.centerm.cpay.common.dto.app.StoreAppInfoDto;
import com.centerm.cpay.common.domain.app.CopsStoreApp;
import java.util.List;

import com.centerm.cpay.common.dto.app.UserAndCloneAppInOutput;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 开放平台-市场应用Mapper接口
 * 
 * <AUTHOR>
 * @date 2020-01-19
 */
@Repository
public interface CopsStoreAppMapper  extends BaseMapper<CopsStoreApp>{
    /**
     * 查询开放平台-市场应用
     * 
     * @param id 开放平台-市场应用ID
     * @return 开放平台-市场应用
     */
    public CopsStoreApp selectCopsStoreAppById(Long id);

    /**
     * 查询开放平台-市场应用列表
     * 
     * @param copsStoreApp 开放平台-市场应用
     * @return 开放平台-市场应用集合
     */
    public List<CopsStoreApp> selectCopsStoreAppList(CopsStoreApp copsStoreApp);

    /**
     * 查询克隆应用列表
     *
     * @param storeAppInfoDto 开放平台应用市场应用信息
     * @return 开放平台克隆应用列表
     */
    public List<StoreAppInfoDto> getCloneAppList(StoreAppInfoDto storeAppInfoDto);

    /**
     * 删除定制开发者克隆信息列表
     *
     * @param apkId 应用id
     * @param devId 开发者id
     * @return 结果
     */
    public int removeCopsCloneApp(@Param("appId") Long apkId, @Param("devId") String devId);

    /**
     * 查询应用市场中我的应用和通用应用
     * @param userAndCloneAppInOutput
     * @return 应用市场中我的应用和通用应用
     */
    public List<UserAndCloneAppInOutput> getUserAppAndCloneAppList(UserAndCloneAppInOutput userAndCloneAppInOutput);
}
