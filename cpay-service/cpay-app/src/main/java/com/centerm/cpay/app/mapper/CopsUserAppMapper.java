package com.centerm.cpay.app.mapper;

import com.centerm.cpay.app.domain.CopsUserApp;

import java.util.List;

import com.centerm.cpay.app.dto.UserAppInfoOutput;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 开放平台-我的应用Mapper接口
 *
 * <AUTHOR>
 * @date 2020-01-17
 */
@Repository
public interface CopsUserAppMapper extends BaseMapper<CopsUserApp> {
    /**
     * 查询开放平台-我的应用
     *
     * @param id 开放平台-我的应用ID
     * @return 开放平台-我的应用
     */
    public CopsUserApp selectCopsUserAppById(Long id);

    /**
     * 查询开放平台-我的应用列表
     *
     * @param copsUserApp 开放平台-我的应用
     * @return 开放平台-我的应用集合
     */
    public List<CopsUserApp> selectCopsUserAppList(CopsUserApp copsUserApp);

    /**
     * 删除开放平台-我的应用
     *
     * @param appId 应用id
     * @return 结果
     */
    public Boolean removeUserAppByAppId(Long appId);

    /**
     * 查询开放平台-我的应用以及应用详情
     *
     * @param userAppInfoOutput 开放平台-我的应用以及应用详情
     * @return 我的应用以及应用详情
     */
    public List<UserAppInfoOutput> getUserAppInfo(UserAppInfoOutput userAppInfoOutput);
}
