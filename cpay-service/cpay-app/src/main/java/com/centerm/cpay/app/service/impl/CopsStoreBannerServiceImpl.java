package com.centerm.cpay.app.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.centerm.cpay.common.domain.app.StoreBanner;
import com.centerm.cpay.common.dto.app.StoreBannerOutput;
import com.centerm.cpay.app.constant.CopsStoreBannerConstants;
import com.centerm.cpay.app.mapper.CopsStoreBannerMapper;
import com.centerm.cpay.app.service.ICopsStoreBannerService;
import org.miser.common.core.support.Convert;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 应用管理--定制banner信息（开放平台）Service业务层处理
 *
 * <AUTHOR>
 * @date 2019-12-20
 */
@Service
public class CopsStoreBannerServiceImpl extends ServiceImpl<CopsStoreBannerMapper, StoreBanner> implements ICopsStoreBannerService {

    @Override
    public List<StoreBannerOutput> selectStoreBannerList(StoreBanner storeBanner) {
        return baseMapper.selectStoreBannerList(storeBanner);
    }

    @Override
    public int insertStoreBanner(StoreBanner storeBanner) {
        storeBanner.setIsDeleted(CopsStoreBannerConstants.NOT_DELETED);
        return baseMapper.insert(storeBanner);
    }

    @Override
    public int updateStoreBanner(StoreBanner storeBanner) {
        return baseMapper.updateById(storeBanner);
    }

    @Override
    public int removeStoreBanner(String ids) {
        return baseMapper.deleteBatchIds(Convert.toLongList(ids));
    }
}
